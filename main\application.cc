#include "application.h"
#include "board.h"
#include "display.h"
#include "system_info.h"
#include "ml307_ssl_transport.h"
#include "audio_codec.h"
#include "mqtt_protocol.h"
#include "websocket_protocol.h"
#include "font_awesome_symbols.h"
#include "iot/thing_manager.h"
#include "assets/lang_config.h"
#include "mcp_server.h"
#include "audio_debugger.h"



#if CONFIG_USE_AUDIO_PROCESSOR
#include "afe_audio_processor.h"
#else
#include "no_audio_processor.h"
#endif

#if CONFIG_USE_AFE_WAKE_WORD
#include "afe_wake_word.h"
#elif CONFIG_USE_ESP_WAKE_WORD
#include "esp_wake_word.h"
#else
#include "no_wake_word.h"
#endif

#include <cstring>
#include <esp_log.h>
#include <cJSON.h>
#include <driver/gpio.h>
#include <arpa/inet.h>
#include <map>
#include <thread>

#define TAG "Application"


static const char* const STATE_STRINGS[] = {
    "unknown",
    "starting",
    "configuring",
    "idle",
    "connecting",
    "listening",
    "speaking",
    "upgrading",
    "activating",
    "audio_testing",
    "fatal_error",
    "invalid_state"
};

Application::Application() {
    event_group_ = xEventGroupCreate();
    background_task_ = new BackgroundTask(4096 * 7);

#if CONFIG_USE_DEVICE_AEC
    aec_mode_ = kAecOnDeviceSide;
#elif CONFIG_USE_SERVER_AEC
    aec_mode_ = kAecOnServerSide;
#else
    aec_mode_ = kAecOff;
#endif

#if CONFIG_USE_AUDIO_PROCESSOR
    audio_processor_ = std::make_unique<AfeAudioProcessor>();
#else
    audio_processor_ = std::make_unique<NoAudioProcessor>();
#endif

#if CONFIG_USE_AFE_WAKE_WORD
    wake_word_ = std::make_unique<AfeWakeWord>();
#elif CONFIG_USE_ESP_WAKE_WORD
    wake_word_ = std::make_unique<EspWakeWord>();
#else
    wake_word_ = std::make_unique<NoWakeWord>();
#endif

    esp_timer_create_args_t clock_timer_args = {
        .callback = [](void* arg) {
            Application* app = (Application*)arg;
            app->OnClockTimer();
        },
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "clock_timer",
        .skip_unhandled_events = true
    };
    esp_timer_create(&clock_timer_args, &clock_timer_handle_);
}

Application::~Application() {
    if (clock_timer_handle_ != nullptr) {
        esp_timer_stop(clock_timer_handle_);
        esp_timer_delete(clock_timer_handle_);
    }
    if (background_task_ != nullptr) {
        delete background_task_;
    }
    vEventGroupDelete(event_group_);
}

void Application::CheckNewVersion(Ota& ota) {
    const int MAX_RETRY = 10;
    int retry_count = 0;
    int retry_delay = 10; // 初始重试延迟为10秒

    while (true) {
        SetDeviceState(kDeviceStateActivating);
        auto display = Board::GetInstance().GetDisplay();
        display->SetStatus(Lang::Strings::CHECKING_NEW_VERSION);

        if (!ota.CheckVersion()) {
            retry_count++;
            if (retry_count >= MAX_RETRY) {
                ESP_LOGE(TAG, "Too many retries, exit version check");
                return;
            }

            char buffer[128];
            snprintf(buffer, sizeof(buffer), Lang::Strings::CHECK_NEW_VERSION_FAILED, retry_delay, ota.GetCheckVersionUrl().c_str());
            Alert(Lang::Strings::ERROR, buffer, "sad", Lang::Sounds::P3_EXCLAMATION);

            ESP_LOGW(TAG, "Check new version failed, retry in %d seconds (%d/%d)", retry_delay, retry_count, MAX_RETRY);
            for (int i = 0; i < retry_delay; i++) {
                vTaskDelay(pdMS_TO_TICKS(1000));
                if (device_state_ == kDeviceStateIdle) {
                    break;
                }
            }
            retry_delay *= 2; // 每次重试后延迟时间翻倍
            continue;
        }
        retry_count = 0;
        retry_delay = 10; // 重置重试延迟时间

        if (ota.HasNewVersion()) {
            Alert(Lang::Strings::OTA_UPGRADE, Lang::Strings::UPGRADING, "happy", Lang::Sounds::P3_UPGRADE);

            vTaskDelay(pdMS_TO_TICKS(3000));

            SetDeviceState(kDeviceStateUpgrading);
            
            display->SetIcon(FONT_AWESOME_DOWNLOAD);
            std::string message = std::string(Lang::Strings::NEW_VERSION) + ota.GetFirmwareVersion();
            display->SetChatMessage("system", message.c_str());

            auto& board = Board::GetInstance();
            board.SetPowerSaveMode(false);
            wake_word_->StopDetection();
            // 预先关闭音频输出，避免升级过程有音频操作
            auto codec = board.GetAudioCodec();
            codec->EnableInput(false);
            codec->EnableOutput(false);
            {
                std::lock_guard<std::mutex> lock(mutex_);
                audio_decode_queue_.clear();
            }
            background_task_->WaitForCompletion();
            delete background_task_;
            background_task_ = nullptr;
            vTaskDelay(pdMS_TO_TICKS(1000));

            ota.StartUpgrade([display](int progress, size_t speed) {
                char buffer[64];
                snprintf(buffer, sizeof(buffer), "%d%% %uKB/s", progress, speed / 1024);
                display->SetChatMessage("system", buffer);
            });

            // If upgrade success, the device will reboot and never reach here
            display->SetStatus(Lang::Strings::UPGRADE_FAILED);
            ESP_LOGI(TAG, "Firmware upgrade failed...");
            vTaskDelay(pdMS_TO_TICKS(3000));
            Reboot();
            return;
        }

        // No new version, mark the current version as valid
        ota.MarkCurrentVersionValid();
        if (!ota.HasActivationCode() && !ota.HasActivationChallenge()) {
            xEventGroupSetBits(event_group_, CHECK_NEW_VERSION_DONE_EVENT);
            // Exit the loop if done checking new version
            break;
        }

        display->SetStatus(Lang::Strings::ACTIVATION);
        // Activation code is shown to the user and waiting for the user to input
        if (ota.HasActivationCode()) {
            ShowActivationCode(ota.GetActivationCode(), ota.GetActivationMessage());
        }

        // This will block the loop until the activation is done or timeout
        for (int i = 0; i < 10; ++i) {
            ESP_LOGI(TAG, "Activating... %d/%d", i + 1, 10);
            esp_err_t err = ota.Activate();
            if (err == ESP_OK) {
                xEventGroupSetBits(event_group_, CHECK_NEW_VERSION_DONE_EVENT);
                break;
            } else if (err == ESP_ERR_TIMEOUT) {
                vTaskDelay(pdMS_TO_TICKS(3000));
            } else {
                vTaskDelay(pdMS_TO_TICKS(10000));
            }
            if (device_state_ == kDeviceStateIdle) {
                break;
            }
        }
    }
}

void Application::ShowActivationCode(const std::string& code, const std::string& message) {
    struct digit_sound {
        char digit;
        const std::string_view& sound;
    };
    static const std::array<digit_sound, 10> digit_sounds{{
        digit_sound{'0', Lang::Sounds::P3_0},
        digit_sound{'1', Lang::Sounds::P3_1}, 
        digit_sound{'2', Lang::Sounds::P3_2},
        digit_sound{'3', Lang::Sounds::P3_3},
        digit_sound{'4', Lang::Sounds::P3_4},
        digit_sound{'5', Lang::Sounds::P3_5},
        digit_sound{'6', Lang::Sounds::P3_6},
        digit_sound{'7', Lang::Sounds::P3_7},
        digit_sound{'8', Lang::Sounds::P3_8},
        digit_sound{'9', Lang::Sounds::P3_9}
    }};

    // This sentence uses 9KB of SRAM, so we need to wait for it to finish
    Alert(Lang::Strings::ACTIVATION, message.c_str(), "happy", Lang::Sounds::P3_ACTIVATION);

    for (const auto& digit : code) {
        auto it = std::find_if(digit_sounds.begin(), digit_sounds.end(),
            [digit](const digit_sound& ds) { return ds.digit == digit; });
        if (it != digit_sounds.end()) {
            PlaySound(it->sound);
        }
    }
}

void Application::Alert(const char* status, const char* message, const char* emotion, const std::string_view& sound) {
    ESP_LOGW(TAG, "Alert %s: %s [%s]", status, message, emotion);
    auto display = Board::GetInstance().GetDisplay();
    display->SetStatus(status);
    display->SetEmotion(emotion);
    display->SetChatMessage("system", message);
    if (!sound.empty()) {
        ResetDecoder();
        PlaySound(sound);
    }
}

void Application::DismissAlert() {
    if (device_state_ == kDeviceStateIdle) {
        auto display = Board::GetInstance().GetDisplay();
        display->SetStatus(Lang::Strings::STANDBY);
        display->SetEmotion("neutral");
        display->SetChatMessage("system", "");
    }
}

void Application::PlaySound(const std::string_view& sound) {
    // Wait for the previous sound to finish
    {
        std::unique_lock<std::mutex> lock(mutex_);
        audio_decode_cv_.wait(lock, [this]() {
            return audio_decode_queue_.empty();
        });
    }
    background_task_->WaitForCompletion();

    const char* data = sound.data();
    size_t size = sound.size();
    for (const char* p = data; p < data + size; ) {
        auto p3 = (BinaryProtocol3*)p;
        p += sizeof(BinaryProtocol3);

        auto payload_size = ntohs(p3->payload_size);
        AudioStreamPacket packet;
        packet.sample_rate = 16000;
        packet.frame_duration = 60;
        packet.payload.resize(payload_size);
        memcpy(packet.payload.data(), p3->payload, payload_size);
        p += payload_size;

        std::lock_guard<std::mutex> lock(mutex_);
        audio_decode_queue_.emplace_back(std::move(packet));
    }
}

void Application::PlaySoundImmediate(const std::string_view& sound) {
    // 立即播放音效，不等待音频队列
    ESP_LOGI(TAG, "🎵 Playing immediate sound (%d bytes)", (int)sound.size());

    // 检查音频系统状态
    auto codec = Board::GetInstance().GetAudioCodec();
    if (!codec) {
        ESP_LOGE(TAG, "❌ Audio codec not available");
        return;
    }

    if (!codec->output_enabled()) {
        ESP_LOGW(TAG, "⚠️ Audio output not enabled, enabling now");
        codec->EnableOutput(true);
    }

    // 如果是紧急警报，清空现有音频队列
    if (is_critical_alert_active_) {
        std::lock_guard<std::mutex> lock(mutex_);
        audio_decode_queue_.clear();
        ESP_LOGI(TAG, "🚨 Cleared audio queue for critical alert");
    }

    // 检查音频队列状态
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ESP_LOGI(TAG, "📊 Current audio queue size: %d", audio_decode_queue_.size());
    }

    const char* data = sound.data();
    size_t size = sound.size();

    if (size == 0 || data == nullptr) {
        ESP_LOGE(TAG, "❌ Sound data is empty or null");
        return;
    }

    ESP_LOGI(TAG, "🎵 Processing sound data: %d bytes at %p", (int)size, data);

    for (const char* p = data; p < data + size; ) {
        auto p3 = (BinaryProtocol3*)p;
        p += sizeof(BinaryProtocol3);

        auto payload_size = ntohs(p3->payload_size);
        AudioStreamPacket packet;
        packet.sample_rate = 16000;
        packet.frame_duration = 60;
        packet.payload.resize(payload_size);
        memcpy(packet.payload.data(), p3->payload, payload_size);
        p += payload_size;

        // 根据是否是紧急警报决定队列位置
        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (is_critical_alert_active_) {
                // 紧急警报音频放在队列最前面
                audio_decode_queue_.emplace_front(std::move(packet));
            } else {
                // 普通音频放在队列前端（保持原有行为）
                audio_decode_queue_.emplace_front(std::move(packet));
            }
        }
    }

    // 重置紧急警报标志
    if (is_critical_alert_active_) {
        is_critical_alert_active_ = false;
        ESP_LOGI(TAG, "🚨 Critical alert audio processing completed");
    }

    ESP_LOGI(TAG, "✅ Immediate sound queued for playback");
}

void Application::EnterAudioTestingMode() {
    ESP_LOGI(TAG, "Entering audio testing mode");
    ResetDecoder();
    SetDeviceState(kDeviceStateAudioTesting);
}

void Application::ExitAudioTestingMode() {
    ESP_LOGI(TAG, "Exiting audio testing mode");
    SetDeviceState(kDeviceStateWifiConfiguring);
    // Copy audio_testing_queue_ to audio_decode_queue_
    std::lock_guard<std::mutex> lock(mutex_);
    audio_decode_queue_ = std::move(audio_testing_queue_);
    audio_decode_cv_.notify_all();
}

void Application::ToggleChatState() {
    if (device_state_ == kDeviceStateActivating) {
        SetDeviceState(kDeviceStateIdle);
        return;
    } else if (device_state_ == kDeviceStateWifiConfiguring) {
        EnterAudioTestingMode();
        return;
    } else if (device_state_ == kDeviceStateAudioTesting) {
        ExitAudioTestingMode();
        return;
    }

    if (!protocol_) {
        ESP_LOGE(TAG, "Protocol not initialized");
        return;
    }

    if (device_state_ == kDeviceStateIdle) {
        Schedule([this]() {
            if (!protocol_->IsAudioChannelOpened()) {
                SetDeviceState(kDeviceStateConnecting);
                if (!protocol_->OpenAudioChannel()) {
                    return;
                }
            }

            SetListeningMode(aec_mode_ == kAecOff ? kListeningModeAutoStop : kListeningModeRealtime);
        });
    } else if (device_state_ == kDeviceStateSpeaking) {
        Schedule([this]() {
            AbortSpeaking(kAbortReasonNone);
        });
    } else if (device_state_ == kDeviceStateListening) {
        Schedule([this]() {
            protocol_->CloseAudioChannel();
        });
    }
}

void Application::StartListening() {
    if (device_state_ == kDeviceStateActivating) {
        SetDeviceState(kDeviceStateIdle);
        return;
    } else if (device_state_ == kDeviceStateWifiConfiguring) {
        EnterAudioTestingMode();
        return;
    }

    if (!protocol_) {
        ESP_LOGE(TAG, "Protocol not initialized");
        return;
    }
    
    if (device_state_ == kDeviceStateIdle) {
        Schedule([this]() {
            if (!protocol_->IsAudioChannelOpened()) {
                SetDeviceState(kDeviceStateConnecting);
                if (!protocol_->OpenAudioChannel()) {
                    return;
                }
            }

            SetListeningMode(kListeningModeManualStop);
        });
    } else if (device_state_ == kDeviceStateSpeaking) {
        Schedule([this]() {
            AbortSpeaking(kAbortReasonNone);
            SetListeningMode(kListeningModeManualStop);
        });
    }
}

void Application::StopListening() {
    if (device_state_ == kDeviceStateAudioTesting) {
        ExitAudioTestingMode();
        return;
    }

    const std::array<int, 3> valid_states = {
        kDeviceStateListening,
        kDeviceStateSpeaking,
        kDeviceStateIdle,
    };
    // If not valid, do nothing
    if (std::find(valid_states.begin(), valid_states.end(), device_state_) == valid_states.end()) {
        return;
    }

    Schedule([this]() {
        if (device_state_ == kDeviceStateListening) {
            protocol_->SendStopListening();
            SetDeviceState(kDeviceStateIdle);
        }
    });
}

void Application::Start() {
    auto& board = Board::GetInstance();
    SetDeviceState(kDeviceStateStarting);

    /* Setup the display */
    auto display = board.GetDisplay();

    /* Setup the audio codec */
    auto codec = board.GetAudioCodec();
    opus_decoder_ = std::make_unique<OpusDecoderWrapper>(codec->output_sample_rate(), 1, OPUS_FRAME_DURATION_MS);
    opus_encoder_ = std::make_unique<OpusEncoderWrapper>(16000, 1, OPUS_FRAME_DURATION_MS);
    opus_encoder_->SetComplexity(0);
    if (aec_mode_ != kAecOff) {
        ESP_LOGI(TAG, "AEC mode: %d, setting opus encoder complexity to 0", aec_mode_);
        opus_encoder_->SetComplexity(0);
    } else {
#if CONFIG_USE_AUDIO_PROCESSOR
        ESP_LOGI(TAG, "Audio processor detected, setting opus encoder complexity to 5");
        opus_encoder_->SetComplexity(5);
#else
        ESP_LOGI(TAG, "Audio processor not detected, setting opus encoder complexity to 0");
        opus_encoder_->SetComplexity(0);
#endif
    }

    if (codec->input_sample_rate() != 16000) {
        input_resampler_.Configure(codec->input_sample_rate(), 16000);
        reference_resampler_.Configure(codec->input_sample_rate(), 16000);
    }
    codec->Start();

#if CONFIG_USE_AUDIO_PROCESSOR
    xTaskCreatePinnedToCore([](void* arg) {
        Application* app = (Application*)arg;
        app->AudioLoop();
        vTaskDelete(NULL);
    }, "audio_loop", 4096 * 2, this, 8, &audio_loop_task_handle_, 1);
#else
    xTaskCreate([](void* arg) {
        Application* app = (Application*)arg;
        app->AudioLoop();
        vTaskDelete(NULL);
    }, "audio_loop", 4096 * 2, this, 8, &audio_loop_task_handle_);
#endif

    /* Start the clock timer to update the status bar */
    esp_timer_start_periodic(clock_timer_handle_, 1000000);

    /* Wait for the network to be ready */
    ESP_LOGI(TAG, "About to start network initialization");
    board.StartNetwork();
    ESP_LOGI(TAG, "Network initialization completed");

    // Update the status bar immediately to show the network state
    display->UpdateStatusBar(true);

    // Check for new firmware version or get the MQTT broker address
    Ota ota;
    CheckNewVersion(ota);

    // Initialize the protocol
    display->SetStatus(Lang::Strings::LOADING_PROTOCOL);

    // Add MCP common tools before initializing the protocol
#if CONFIG_IOT_PROTOCOL_MCP
    McpServer::GetInstance().AddCommonTools();
#endif

    if (ota.HasMqttConfig()) {
        protocol_ = std::make_unique<MqttProtocol>();
    } else if (ota.HasWebsocketConfig()) {
        protocol_ = std::make_unique<WebsocketProtocol>();
    } else {
        ESP_LOGW(TAG, "No protocol specified in the OTA config, using MQTT");
        protocol_ = std::make_unique<MqttProtocol>();
    }

    protocol_->OnNetworkError([this](const std::string& message) {
        SetDeviceState(kDeviceStateIdle);
        Alert(Lang::Strings::ERROR, message.c_str(), "sad", Lang::Sounds::P3_EXCLAMATION);
    });
    protocol_->OnIncomingAudio([this](AudioStreamPacket&& packet) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (device_state_ == kDeviceStateSpeaking && audio_decode_queue_.size() < MAX_AUDIO_PACKETS_IN_QUEUE) {
            audio_decode_queue_.emplace_back(std::move(packet));
        }
    });
    protocol_->OnAudioChannelOpened([this, codec, &board]() {
        board.SetPowerSaveMode(false);
        if (protocol_->server_sample_rate() != codec->output_sample_rate()) {
            ESP_LOGW(TAG, "Server sample rate %d does not match device output sample rate %d, resampling may cause distortion",
                protocol_->server_sample_rate(), codec->output_sample_rate());
        }

#if CONFIG_IOT_PROTOCOL_XIAOZHI
        auto& thing_manager = iot::ThingManager::GetInstance();
        protocol_->SendIotDescriptors(thing_manager.GetDescriptorsJson());
        std::string states;
        if (thing_manager.GetStatesJson(states, false)) {
            protocol_->SendIotStates(states);
        }
#endif
    });
    protocol_->OnAudioChannelClosed([this, &board]() {
        board.SetPowerSaveMode(true);
        Schedule([this]() {
            auto display = Board::GetInstance().GetDisplay();
            display->SetChatMessage("system", "");
            SetDeviceState(kDeviceStateIdle);
        });
    });
    protocol_->OnIncomingJson([this, display](const cJSON* root) {
        // Parse JSON data
        auto type = cJSON_GetObjectItem(root, "type");
        if (strcmp(type->valuestring, "tts") == 0) {
            auto state = cJSON_GetObjectItem(root, "state");
            if (strcmp(state->valuestring, "start") == 0) {
                Schedule([this]() {
                    aborted_ = false;
                    if (device_state_ == kDeviceStateIdle || device_state_ == kDeviceStateListening) {
                        SetDeviceState(kDeviceStateSpeaking);
                    }
                });
            } else if (strcmp(state->valuestring, "stop") == 0) {
                Schedule([this]() {
                    background_task_->WaitForCompletion();
                    if (device_state_ == kDeviceStateSpeaking) {
                        if (listening_mode_ == kListeningModeManualStop) {
                            SetDeviceState(kDeviceStateIdle);
                        } else {
                            SetDeviceState(kDeviceStateListening);
                        }
                    }
                });
            } else if (strcmp(state->valuestring, "sentence_start") == 0) {
                auto text = cJSON_GetObjectItem(root, "text");
                if (cJSON_IsString(text)) {
                    ESP_LOGI(TAG, "<< %s", text->valuestring);
                    Schedule([this, display, message = std::string(text->valuestring)]() {
                        display->SetChatMessage("assistant", message.c_str());
                    });
                }
            }
        } else if (strcmp(type->valuestring, "stt") == 0) {
            auto text = cJSON_GetObjectItem(root, "text");
            if (cJSON_IsString(text)) {
                ESP_LOGI(TAG, ">> %s", text->valuestring);
                Schedule([this, display, message = std::string(text->valuestring)]() {
                    display->SetChatMessage("user", message.c_str());
                });
            }
        } else if (strcmp(type->valuestring, "llm") == 0) {
            auto emotion = cJSON_GetObjectItem(root, "emotion");
            if (cJSON_IsString(emotion)) {
                Schedule([this, display, emotion_str = std::string(emotion->valuestring)]() {
                    display->SetEmotion(emotion_str.c_str());
                });
            }
#if CONFIG_IOT_PROTOCOL_MCP
        } else if (strcmp(type->valuestring, "mcp") == 0) {
            auto payload = cJSON_GetObjectItem(root, "payload");
            if (cJSON_IsObject(payload)) {
                McpServer::GetInstance().ParseMessage(payload);
            }
#endif
#if CONFIG_IOT_PROTOCOL_XIAOZHI
        } else if (strcmp(type->valuestring, "iot") == 0) {
            auto commands = cJSON_GetObjectItem(root, "commands");
            if (cJSON_IsArray(commands)) {
                auto& thing_manager = iot::ThingManager::GetInstance();
                for (int i = 0; i < cJSON_GetArraySize(commands); ++i) {
                    auto command = cJSON_GetArrayItem(commands, i);
                    thing_manager.Invoke(command);
                }
            }
#endif
        } else if (strcmp(type->valuestring, "system") == 0) {
            auto command = cJSON_GetObjectItem(root, "command");
            if (cJSON_IsString(command)) {
                ESP_LOGI(TAG, "System command: %s", command->valuestring);
                if (strcmp(command->valuestring, "reboot") == 0) {
                    // Do a reboot if user requests a OTA update
                    Schedule([this]() {
                        Reboot();
                    });
                } else {
                    ESP_LOGW(TAG, "Unknown system command: %s", command->valuestring);
                }
            }
        } else if (strcmp(type->valuestring, "alert") == 0) {
            auto status = cJSON_GetObjectItem(root, "status");
            auto message = cJSON_GetObjectItem(root, "message");
            auto emotion = cJSON_GetObjectItem(root, "emotion");
            if (cJSON_IsString(status) && cJSON_IsString(message) && cJSON_IsString(emotion)) {
                Alert(status->valuestring, message->valuestring, emotion->valuestring, Lang::Sounds::P3_VIBRATION);
            } else {
                ESP_LOGW(TAG, "Alert command requires status, message and emotion");
            }
        } else {
            ESP_LOGW(TAG, "Unknown message type: %s", type->valuestring);
        }
    });
    bool protocol_started = protocol_->Start();

    audio_debugger_ = std::make_unique<AudioDebugger>();
    audio_processor_->Initialize(codec);
    audio_processor_->OnOutput([this](std::vector<int16_t>&& data) {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (audio_send_queue_.size() >= MAX_AUDIO_PACKETS_IN_QUEUE) {
                ESP_LOGW(TAG, "Too many audio packets in queue, drop the newest packet");
                return;
            }
        }
        background_task_->Schedule([this, data = std::move(data)]() mutable {
            opus_encoder_->Encode(std::move(data), [this](std::vector<uint8_t>&& opus) {
                AudioStreamPacket packet;
                packet.payload = std::move(opus);
#ifdef CONFIG_USE_SERVER_AEC
                {
                    std::lock_guard<std::mutex> lock(timestamp_mutex_);
                    if (!timestamp_queue_.empty()) {
                        packet.timestamp = timestamp_queue_.front();
                        timestamp_queue_.pop_front();
                    } else {
                        packet.timestamp = 0;
                    }

                    if (timestamp_queue_.size() > 3) { // 限制队列长度3
                        timestamp_queue_.pop_front(); // 该包发送前先出队保持队列长度
                        return;
                    }
                }
#endif
                std::lock_guard<std::mutex> lock(mutex_);
                if (audio_send_queue_.size() >= MAX_AUDIO_PACKETS_IN_QUEUE) {
                    ESP_LOGW(TAG, "Too many audio packets in queue, drop the oldest packet");
                    audio_send_queue_.pop_front();
                }
                audio_send_queue_.emplace_back(std::move(packet));
                xEventGroupSetBits(event_group_, SEND_AUDIO_EVENT);
            });
        });
    });
    audio_processor_->OnVadStateChange([this](bool speaking) {
        if (device_state_ == kDeviceStateListening) {
            Schedule([this, speaking]() {
                if (speaking) {
                    voice_detected_ = true;
                } else {
                    voice_detected_ = false;
                }
                auto led = Board::GetInstance().GetLed();
                led->OnStateChanged();
            });
        }
    });

    wake_word_->Initialize(codec);
    wake_word_->OnWakeWordDetected([this](const std::string& wake_word) {
        Schedule([this, &wake_word]() {
            if (!protocol_) {
                return;
            }

            if (device_state_ == kDeviceStateIdle) {
                wake_word_->EncodeWakeWordData();

                if (!protocol_->IsAudioChannelOpened()) {
                    SetDeviceState(kDeviceStateConnecting);
                    if (!protocol_->OpenAudioChannel()) {
                        wake_word_->StartDetection();
                        return;
                    }
                }

                ESP_LOGI(TAG, "Wake word detected: %s", wake_word.c_str());
#if CONFIG_USE_AFE_WAKE_WORD
                AudioStreamPacket packet;
                // Encode and send the wake word data to the server
                while (wake_word_->GetWakeWordOpus(packet.payload)) {
                    protocol_->SendAudio(packet);
                }
                // Set the chat state to wake word detected
                protocol_->SendWakeWordDetected(wake_word);
#else
                // Play the pop up sound to indicate the wake word is detected
                // And wait 60ms to make sure the queue has been processed by audio task
                ResetDecoder();
                PlaySound(Lang::Sounds::P3_POPUP);
                vTaskDelay(pdMS_TO_TICKS(60));
#endif
                SetListeningMode(aec_mode_ == kAecOff ? kListeningModeAutoStop : kListeningModeRealtime);
            } else if (device_state_ == kDeviceStateSpeaking) {
                AbortSpeaking(kAbortReasonWakeWordDetected);
            } else if (device_state_ == kDeviceStateActivating) {
                SetDeviceState(kDeviceStateIdle);
            }
        });
    });
    wake_word_->StartDetection();

    // Wait for the new version check to finish
    xEventGroupWaitBits(event_group_, CHECK_NEW_VERSION_DONE_EVENT, pdTRUE, pdFALSE, portMAX_DELAY);
    SetDeviceState(kDeviceStateIdle);

    has_server_time_ = ota.HasServerTime();
    if (protocol_started) {
        std::string message = std::string(Lang::Strings::VERSION) + ota.GetCurrentVersion();
        display->ShowNotification(message.c_str());
        display->SetChatMessage("system", "");
        // Play the success sound to indicate the device is ready
        ResetDecoder();
        PlaySound(Lang::Sounds::P3_SUCCESS);
    }

    // Print heap stats
    SystemInfo::PrintHeapStats();

    // Initialize WiFi data receiver
    InitializeWifiDataReceiver();

    // BLE功能暂时移除

    // Enter the main event loop
    MainEventLoop();
}

void Application::OnClockTimer() {
    clock_ticks_++;

    auto display = Board::GetInstance().GetDisplay();
    display->UpdateStatusBar();

    // Print the debug info every 10 seconds
    if (clock_ticks_ % 10 == 0) {
        // SystemInfo::PrintTaskCpuUsage(pdMS_TO_TICKS(1000));
        // SystemInfo::PrintTaskList();
        SystemInfo::PrintHeapStats();

        // If we have synchronized server time, set the status to clock "HH:MM" if the device is idle
        if (has_server_time_) {
            if (device_state_ == kDeviceStateIdle) {
                Schedule([this]() {
                    // Set status to clock "HH:MM"
                    time_t now = time(NULL);
                    char time_str[64];
                    strftime(time_str, sizeof(time_str), "%H:%M  ", localtime(&now));
                    Board::GetInstance().GetDisplay()->SetStatus(time_str);
                });
            }
        }
    }
}

// Add a async task to MainLoop
void Application::Schedule(std::function<void()> callback) {
    {
        std::lock_guard<std::mutex> lock(mutex_);
        main_tasks_.push_back(std::move(callback));
    }
    xEventGroupSetBits(event_group_, SCHEDULE_EVENT);
}

// The Main Event Loop controls the chat state and websocket connection
// If other tasks need to access the websocket or chat state,
// they should use Schedule to call this function
void Application::MainEventLoop() {
    // Raise the priority of the main event loop to avoid being interrupted by background tasks (which has priority 2)
    vTaskPrioritySet(NULL, 3);

    while (true) {
        auto bits = xEventGroupWaitBits(event_group_, SCHEDULE_EVENT | SEND_AUDIO_EVENT, pdTRUE, pdFALSE, portMAX_DELAY);

        if (bits & SEND_AUDIO_EVENT) {
            std::unique_lock<std::mutex> lock(mutex_);
            auto packets = std::move(audio_send_queue_);
            lock.unlock();
            for (auto& packet : packets) {
                if (!protocol_->SendAudio(packet)) {
                    break;
                }
            }
        }

        if (bits & SCHEDULE_EVENT) {
            std::unique_lock<std::mutex> lock(mutex_);
            auto tasks = std::move(main_tasks_);
            lock.unlock();
            for (auto& task : tasks) {
                task();
            }
        }
    }
}

// The Audio Loop is used to input and output audio data
void Application::AudioLoop() {
    auto codec = Board::GetInstance().GetAudioCodec();
    while (true) {
        OnAudioInput();
        if (codec->output_enabled()) {
            OnAudioOutput();
        }
    }
}

void Application::OnAudioOutput() {
    if (busy_decoding_audio_) {
        return;
    }

    auto now = std::chrono::steady_clock::now();
    auto codec = Board::GetInstance().GetAudioCodec();
    const int max_silence_seconds = 10;

    std::unique_lock<std::mutex> lock(mutex_);
    if (audio_decode_queue_.empty()) {
        // Disable the output if there is no audio data for a long time
        if (device_state_ == kDeviceStateIdle) {
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - last_output_time_).count();
            if (duration > max_silence_seconds) {
                codec->EnableOutput(false);
            }
        }
        return;
    }

    auto packet = std::move(audio_decode_queue_.front());
    audio_decode_queue_.pop_front();
    lock.unlock();
    audio_decode_cv_.notify_all();

    // Synchronize the sample rate and frame duration
    SetDecodeSampleRate(packet.sample_rate, packet.frame_duration);

    busy_decoding_audio_ = true;
    if (!background_task_->Schedule([this, codec, packet = std::move(packet)]() mutable {
        busy_decoding_audio_ = false;
        if (aborted_) {
            return;
        }

        std::vector<int16_t> pcm;
        if (!opus_decoder_->Decode(std::move(packet.payload), pcm)) {
            return;
        }
        // Resample if the sample rate is different
        if (opus_decoder_->sample_rate() != codec->output_sample_rate()) {
            int target_size = output_resampler_.GetOutputSamples(pcm.size());
            std::vector<int16_t> resampled(target_size);
            output_resampler_.Process(pcm.data(), pcm.size(), resampled.data());
            pcm = std::move(resampled);
        }
        codec->OutputData(pcm);
#ifdef CONFIG_USE_SERVER_AEC
        std::lock_guard<std::mutex> lock(timestamp_mutex_);
        timestamp_queue_.push_back(packet.timestamp);
#endif
        last_output_time_ = std::chrono::steady_clock::now();
    })) {
        busy_decoding_audio_ = false;
    }
}

void Application::OnAudioInput() {
    if (device_state_ == kDeviceStateAudioTesting) {
        if (audio_testing_queue_.size() >= AUDIO_TESTING_MAX_DURATION_MS / OPUS_FRAME_DURATION_MS) {
            ExitAudioTestingMode();
            return;
        }
        std::vector<int16_t> data;
        int samples = OPUS_FRAME_DURATION_MS * 16000 / 1000;
        if (ReadAudio(data, 16000, samples)) {
            background_task_->Schedule([this, data = std::move(data)]() mutable {
                opus_encoder_->Encode(std::move(data), [this](std::vector<uint8_t>&& opus) {
                    AudioStreamPacket packet;
                    packet.payload = std::move(opus);
                    packet.frame_duration = OPUS_FRAME_DURATION_MS;
                    packet.sample_rate = 16000;
                    std::lock_guard<std::mutex> lock(mutex_);
                    audio_testing_queue_.push_back(std::move(packet));
                });
            });
            return;
        }
    }

    if (wake_word_->IsDetectionRunning()) {
        std::vector<int16_t> data;
        int samples = wake_word_->GetFeedSize();
        if (samples > 0) {
            if (ReadAudio(data, 16000, samples)) {
                wake_word_->Feed(data);
                return;
            }
        }
    }

    if (audio_processor_->IsRunning()) {
        std::vector<int16_t> data;
        int samples = audio_processor_->GetFeedSize();
        if (samples > 0) {
            if (ReadAudio(data, 16000, samples)) {
                audio_processor_->Feed(data);
                return;
            }
        }
    }

    vTaskDelay(pdMS_TO_TICKS(OPUS_FRAME_DURATION_MS / 2));
}

bool Application::ReadAudio(std::vector<int16_t>& data, int sample_rate, int samples) {
    auto codec = Board::GetInstance().GetAudioCodec();
    if (!codec->input_enabled()) {
        return false;
    }

    if (codec->input_sample_rate() != sample_rate) {
        data.resize(samples * codec->input_sample_rate() / sample_rate);
        if (!codec->InputData(data)) {
            return false;
        }
        if (codec->input_channels() == 2) {
            auto mic_channel = std::vector<int16_t>(data.size() / 2);
            auto reference_channel = std::vector<int16_t>(data.size() / 2);
            for (size_t i = 0, j = 0; i < mic_channel.size(); ++i, j += 2) {
                mic_channel[i] = data[j];
                reference_channel[i] = data[j + 1];
            }
            auto resampled_mic = std::vector<int16_t>(input_resampler_.GetOutputSamples(mic_channel.size()));
            auto resampled_reference = std::vector<int16_t>(reference_resampler_.GetOutputSamples(reference_channel.size()));
            input_resampler_.Process(mic_channel.data(), mic_channel.size(), resampled_mic.data());
            reference_resampler_.Process(reference_channel.data(), reference_channel.size(), resampled_reference.data());
            data.resize(resampled_mic.size() + resampled_reference.size());
            for (size_t i = 0, j = 0; i < resampled_mic.size(); ++i, j += 2) {
                data[j] = resampled_mic[i];
                data[j + 1] = resampled_reference[i];
            }
        } else {
            auto resampled = std::vector<int16_t>(input_resampler_.GetOutputSamples(data.size()));
            input_resampler_.Process(data.data(), data.size(), resampled.data());
            data = std::move(resampled);
        }
    } else {
        data.resize(samples);
        if (!codec->InputData(data)) {
            return false;
        }
    }
    
    // 音频调试：发送原始音频数据
    if (audio_debugger_) {
        audio_debugger_->Feed(data);
    }
    
    return true;
}

void Application::AbortSpeaking(AbortReason reason) {
    ESP_LOGI(TAG, "Abort speaking");
    aborted_ = true;
    protocol_->SendAbortSpeaking(reason);
}

void Application::SetListeningMode(ListeningMode mode) {
    listening_mode_ = mode;
    SetDeviceState(kDeviceStateListening);
}

void Application::SetDeviceState(DeviceState state) {
    if (device_state_ == state) {
        return;
    }
    
    clock_ticks_ = 0;
    auto previous_state = device_state_;
    device_state_ = state;
    ESP_LOGI(TAG, "STATE: %s", STATE_STRINGS[device_state_]);
    // The state is changed, wait for all background tasks to finish
    background_task_->WaitForCompletion();

    auto& board = Board::GetInstance();
    auto display = board.GetDisplay();
    auto led = board.GetLed();
    led->OnStateChanged();
    switch (state) {
        case kDeviceStateUnknown:
        case kDeviceStateIdle:
            display->SetStatus(Lang::Strings::STANDBY);
            display->SetEmotion("neutral");
            audio_processor_->Stop();
            wake_word_->StartDetection();
            break;
        case kDeviceStateConnecting:
            display->SetStatus(Lang::Strings::CONNECTING);
            display->SetEmotion("neutral");
            display->SetChatMessage("system", "");
            timestamp_queue_.clear();
            break;
        case kDeviceStateListening:
            display->SetStatus(Lang::Strings::LISTENING);
            display->SetEmotion("neutral");
            // Update the IoT states before sending the start listening command
#if CONFIG_IOT_PROTOCOL_XIAOZHI
            UpdateIotStates();
#endif

            // Make sure the audio processor is running
            if (!audio_processor_->IsRunning()) {
                // Send the start listening command
                protocol_->SendStartListening(listening_mode_);
                if (previous_state == kDeviceStateSpeaking) {
                    audio_decode_queue_.clear();
                    audio_decode_cv_.notify_all();
                    // FIXME: Wait for the speaker to empty the buffer
                    vTaskDelay(pdMS_TO_TICKS(120));
                }
                opus_encoder_->ResetState();
                audio_processor_->Start();
                wake_word_->StopDetection();
            }
            break;
        case kDeviceStateSpeaking:
            display->SetStatus(Lang::Strings::SPEAKING);

            if (listening_mode_ != kListeningModeRealtime) {
                audio_processor_->Stop();
                // Only AFE wake word can be detected in speaking mode
#if CONFIG_USE_AFE_WAKE_WORD
                wake_word_->StartDetection();
#else
                wake_word_->StopDetection();
#endif
            }
            ResetDecoder();
            break;
        default:
            // Do nothing
            break;
    }
}

void Application::ResetDecoder() {
    std::lock_guard<std::mutex> lock(mutex_);
    opus_decoder_->ResetState();
    audio_decode_queue_.clear();
    audio_decode_cv_.notify_all();
    last_output_time_ = std::chrono::steady_clock::now();
    auto codec = Board::GetInstance().GetAudioCodec();
    codec->EnableOutput(true);
}

void Application::SetDecodeSampleRate(int sample_rate, int frame_duration) {
    if (opus_decoder_->sample_rate() == sample_rate && opus_decoder_->duration_ms() == frame_duration) {
        return;
    }

    opus_decoder_.reset();
    opus_decoder_ = std::make_unique<OpusDecoderWrapper>(sample_rate, 1, frame_duration);

    auto codec = Board::GetInstance().GetAudioCodec();
    if (opus_decoder_->sample_rate() != codec->output_sample_rate()) {
        ESP_LOGI(TAG, "Resampling audio from %d to %d", opus_decoder_->sample_rate(), codec->output_sample_rate());
        output_resampler_.Configure(opus_decoder_->sample_rate(), codec->output_sample_rate());
    }
}

void Application::UpdateIotStates() {
#if CONFIG_IOT_PROTOCOL_XIAOZHI
    auto& thing_manager = iot::ThingManager::GetInstance();
    std::string states;
    if (thing_manager.GetStatesJson(states, true)) {
        protocol_->SendIotStates(states);
    }
#endif
}

void Application::Reboot() {
    ESP_LOGI(TAG, "Rebooting...");
    esp_restart();
}

void Application::WakeWordInvoke(const std::string& wake_word) {
    if (device_state_ == kDeviceStateIdle) {
        ToggleChatState();
        Schedule([this, wake_word]() {
            if (protocol_) {
                protocol_->SendWakeWordDetected(wake_word); 
            }
        }); 
    } else if (device_state_ == kDeviceStateSpeaking) {
        Schedule([this]() {
            AbortSpeaking(kAbortReasonNone);
        });
    } else if (device_state_ == kDeviceStateListening) {   
        Schedule([this]() {
            if (protocol_) {
                protocol_->CloseAudioChannel();
            }
        });
    }
}

bool Application::CanEnterSleepMode() {
    if (device_state_ != kDeviceStateIdle) {
        return false;
    }

    if (protocol_ && protocol_->IsAudioChannelOpened()) {
        return false;
    }

    // Now it is safe to enter sleep mode
    return true;
}

void Application::SendMcpMessage(const std::string& payload) {
    Schedule([this, payload]() {
        if (protocol_) {
            protocol_->SendMcpMessage(payload);
        }
    });
}

void Application::SendTtsRequest(const std::string& text) {
    Schedule([this, text]() {
        if (protocol_) {
            ESP_LOGI(TAG, "🗣️ Sending TTS request: %s", text.c_str());
            protocol_->SendTtsRequest(text);
        }
    });
}

void Application::SetAecMode(AecMode mode) {
    aec_mode_ = mode;
    Schedule([this]() {
        auto& board = Board::GetInstance();
        auto display = board.GetDisplay();
        switch (aec_mode_) {
        case kAecOff:
            audio_processor_->EnableDeviceAec(false);
            display->ShowNotification(Lang::Strings::RTC_MODE_OFF);
            break;
        case kAecOnServerSide:
            audio_processor_->EnableDeviceAec(false);
            display->ShowNotification(Lang::Strings::RTC_MODE_ON);
            break;
        case kAecOnDeviceSide:
            audio_processor_->EnableDeviceAec(true);
            display->ShowNotification(Lang::Strings::RTC_MODE_ON);
            break;
        }

        // If the AEC mode is changed, close the audio channel
        if (protocol_ && protocol_->IsAudioChannelOpened()) {
            protocol_->CloseAudioChannel();
        }
    });
}

void Application::InitializeWifiDataReceiver() {
    ESP_LOGI(TAG, "Initializing WiFi data receiver...");

    wifi_data_receiver_config_t config = wifi_data_receiver_get_default_config();
    config.port = 8080;  // 使用端口8080与PlatformIO发送端匹配
    config.max_clients = 3;
    config.recv_timeout_ms = 10000;
    config.auto_start = true;

    esp_err_t ret = wifi_data_receiver_init(&config);
    if (ret == ESP_OK) {
        wifi_data_receiver_initialized_ = true;
        wifi_data_receiver_enabled_ = true;

        // 设置回调函数
        wifi_data_receiver_set_data_callback(OnWifiDataReceived, this);
        wifi_data_receiver_set_client_callback(OnWifiClientStatus, this);

        // 获取并显示服务器信息
        char ip_str[16];
        if (wifi_data_receiver_get_server_ip(ip_str, sizeof(ip_str)) == ESP_OK) {
            ESP_LOGI(TAG, "📡 WiFi Data Receiver started successfully!");
            ESP_LOGI(TAG, "🌐 Server IP: %s", ip_str);
            ESP_LOGI(TAG, "🔌 Server Port: %d", config.port);
            ESP_LOGI(TAG, "👥 Max Clients: %d", config.max_clients);
            ESP_LOGI(TAG, "");
            ESP_LOGI(TAG, "📋 Configuration for PlatformIO sender:");
            ESP_LOGI(TAG, "   wifi_serial_target_ip = \"%s\"", ip_str);
            ESP_LOGI(TAG, "   wifi_serial_target_port = %d", config.port);
            ESP_LOGI(TAG, "");
            ESP_LOGI(TAG, "✅ Ready to receive data from PlatformIO sender!");

            // 在显示屏上显示服务器信息
            auto display = Board::GetInstance().GetDisplay();
            std::string notification = "WiFi接收器已启动\nIP: ";
            notification += ip_str;
            notification += ":";
            notification += std::to_string(config.port);
            display->ShowNotification(notification.c_str(), 10000);
        }
    } else {
        ESP_LOGE(TAG, "Failed to initialize WiFi data receiver: %s", esp_err_to_name(ret));
        wifi_data_receiver_initialized_ = false;
        wifi_data_receiver_enabled_ = false;
    }
}

void Application::StartWifiDataReceiver() {
    if (!wifi_data_receiver_initialized_) {
        ESP_LOGW(TAG, "WiFi data receiver not initialized");
        return;
    }

    if (wifi_data_receiver_is_running()) {
        ESP_LOGW(TAG, "WiFi data receiver already running");
        return;
    }

    esp_err_t ret = wifi_data_receiver_start();
    if (ret == ESP_OK) {
        wifi_data_receiver_enabled_ = true;
        ESP_LOGI(TAG, "WiFi data receiver started");
    } else {
        ESP_LOGE(TAG, "Failed to start WiFi data receiver: %s", esp_err_to_name(ret));
    }
}

void Application::StopWifiDataReceiver() {
    if (!wifi_data_receiver_initialized_) {
        ESP_LOGW(TAG, "WiFi data receiver not initialized");
        return;
    }

    esp_err_t ret = wifi_data_receiver_stop();
    if (ret == ESP_OK) {
        wifi_data_receiver_enabled_ = false;
        ESP_LOGI(TAG, "WiFi data receiver stopped");
    } else {
        ESP_LOGE(TAG, "Failed to stop WiFi data receiver: %s", esp_err_to_name(ret));
    }
}

void Application::OnWifiDataReceived(const wifi_data_packet_t* packet, void* user_data) {
    Application* app = static_cast<Application*>(user_data);

    ESP_LOGI(TAG, "=== WiFi Data Received ===");
    ESP_LOGI(TAG, "From: %s:%d", packet->client_ip, packet->client_port);
    ESP_LOGI(TAG, "Length: %d bytes", packet->length);
    ESP_LOGI(TAG, "Data: %s", packet->data);

    // 尝试解析JSON数据
    cJSON *json = cJSON_Parse(packet->data);
    if (json != NULL) {
        ESP_LOGI(TAG, "=== JSON Data Parsed ===");

        // 解析时间戳
        cJSON *timestamp = cJSON_GetObjectItem(json, "timestamp");
        if (cJSON_IsNumber(timestamp)) {
            ESP_LOGI(TAG, "Timestamp: %d", timestamp->valueint);
        }

        // 解析表情状态
        cJSON *emotion = cJSON_GetObjectItem(json, "emotion");
        if (cJSON_IsString(emotion)) {
            const char* emotion_str = emotion->valuestring;
            ESP_LOGI(TAG, "🎭 Emotion: %s", emotion_str);

            // 处理表情数据并进行智能提醒
            std::string emotion_value = std::string(emotion_str);

            // 只有检测到有效表情时才处理（排除"No Face"）
            if (emotion_value != "No Face") {
                app->ProcessEmotionData(emotion_value);
            }

            // 在显示屏上设置表情
            app->Schedule([app, emotion_value]() {
                auto display = Board::GetInstance().GetDisplay();

                // 将英文表情转换为小智表情显示格式
                std::string xiaozhi_emotion = "neutral";
                if (emotion_value == "Happy") {
                    xiaozhi_emotion = "happy";
                } else if (emotion_value == "Sad") {
                    xiaozhi_emotion = "sad";
                } else if (emotion_value == "Angry") {
                    xiaozhi_emotion = "angry";
                } else if (emotion_value == "Excited") {
                    xiaozhi_emotion = "excited";
                } else if (emotion_value == "Calm") {
                    xiaozhi_emotion = "calm";
                }

                display->SetEmotion(xiaozhi_emotion.c_str());

                // 显示表情通知（缩短显示时间）
                std::string notification = "😊 表情: ";
                notification += emotion_value;
                display->ShowNotification(notification.c_str(), 2000);
            });
        }

        // 解析坐姿状态
        cJSON *posture = cJSON_GetObjectItem(json, "posture");
        if (cJSON_IsString(posture)) {
            const char* posture_str = posture->valuestring;
            ESP_LOGI(TAG, "🪑 Posture: %s", posture_str);

            // 处理坐姿数据并进行智能提醒
            std::string posture_value = std::string(posture_str);

            // 只有检测到有效坐姿时才处理（排除"No People"）
            if (posture_value != "No People") {
                app->ProcessPostureData(posture_value);
            }

            // 在显示屏上显示坐姿状态
            app->Schedule([app, posture_value]() {
                auto display = Board::GetInstance().GetDisplay();

                std::string notification = "🪑 坐姿: ";
                notification += posture_value;
                display->ShowNotification(notification.c_str(), 2000);
            });
        }

        // 解析置信度
        cJSON *confidence = cJSON_GetObjectItem(json, "confidence");
        if (cJSON_IsNumber(confidence)) {
            ESP_LOGI(TAG, "📊 Confidence: %.2f", confidence->valuedouble);
        }

        // 解析传感器数据（保留原有功能）
        cJSON *sensor_data = cJSON_GetObjectItem(json, "sensor_data");
        if (cJSON_IsObject(sensor_data)) {
            cJSON *temperature = cJSON_GetObjectItem(sensor_data, "temperature");
            cJSON *humidity = cJSON_GetObjectItem(sensor_data, "humidity");
            cJSON *pressure = cJSON_GetObjectItem(sensor_data, "pressure");

            if (cJSON_IsNumber(temperature)) {
                ESP_LOGI(TAG, "Temperature: %.2f°C", temperature->valuedouble);
            }
            if (cJSON_IsNumber(humidity)) {
                ESP_LOGI(TAG, "Humidity: %.2f%%", humidity->valuedouble);
            }
            if (cJSON_IsNumber(pressure)) {
                ESP_LOGI(TAG, "Pressure: %.2f hPa", pressure->valuedouble);
            }
        }

        // 解析消息（保留原有功能）
        cJSON *message = cJSON_GetObjectItem(json, "message");
        if (cJSON_IsString(message)) {
            ESP_LOGI(TAG, "Message: %s", message->valuestring);

            // 在显示屏上显示消息
            app->Schedule([app, msg = std::string(message->valuestring)]() {
                auto display = Board::GetInstance().GetDisplay();
                std::string notification = "收到消息: ";
                notification += msg;
                display->ShowNotification(notification.c_str(), 5000);
            });
        }

        // 解析设备状态（保留原有功能）
        cJSON *device_status = cJSON_GetObjectItem(json, "device_status");
        if (cJSON_IsString(device_status)) {
            ESP_LOGI(TAG, "Device Status: %s", device_status->valuestring);
        }

        cJSON_Delete(json);
    } else {
        ESP_LOGI(TAG, "Received non-JSON data");

        // 对于非JSON数据，也在显示屏上显示
        app->Schedule([app, data = std::string(packet->data, packet->length)]() {
            auto display = Board::GetInstance().GetDisplay();
            std::string notification = "收到数据: ";
            notification += data;
            display->ShowNotification(notification.c_str(), 5000);
        });
    }

    // 🔄 转发数据到DevKitC via BLE
    if (app->ble_connected_) {
        ESP_LOGI(TAG, "📡 Forwarding data to DevKitC via BLE...");

        // 创建转发数据包，包含源信息
        std::string forward_data = "[PICO-WiFi] ";
        forward_data.append(packet->data, packet->length);

        if (app->SendDataViaBle((const uint8_t*)forward_data.c_str(), forward_data.length())) {
            ESP_LOGI(TAG, "✅ Data forwarded to DevKitC successfully");
        } else {
            ESP_LOGE(TAG, "❌ Failed to forward data to DevKitC");
        }
    } else {
        ESP_LOGD(TAG, "⚠️ DevKitC not connected, data not forwarded");
    }

    ESP_LOGI(TAG, "====================");
}

void Application::OnWifiClientStatus(const char* client_ip, uint16_t client_port, bool connected, void* user_data) {
    Application* app = static_cast<Application*>(user_data);

    if (connected) {
        ESP_LOGI(TAG, "🔗 WiFi Client connected: %s:%d", client_ip, client_port);

        app->Schedule([app, ip = std::string(client_ip), port = client_port]() {
            auto display = Board::GetInstance().GetDisplay();
            std::string notification = "客户端已连接\n";
            notification += ip;
            notification += ":";
            notification += std::to_string(port);
            display->ShowNotification(notification.c_str(), 3000);
        });
    } else {
        ESP_LOGI(TAG, "❌ WiFi Client disconnected: %s:%d", client_ip, client_port);

        app->Schedule([app, ip = std::string(client_ip), port = client_port]() {
            auto display = Board::GetInstance().GetDisplay();
            std::string notification = "客户端已断开\n";
            notification += ip;
            notification += ":";
            notification += std::to_string(port);
            display->ShowNotification(notification.c_str(), 3000);
        });
    }

    int client_count = wifi_data_receiver_get_client_count();
    ESP_LOGI(TAG, "Total connected clients: %d", client_count);
}

void Application::ProcessEmotionData(const std::string& emotion) {
    // 添加新的情绪数据到历史记录
    data_history_.emotion_history.push_back(emotion);

    // 保持历史记录大小不超过限制
    if (data_history_.emotion_history.size() > DataHistory::MAX_HISTORY_SIZE) {
        data_history_.emotion_history.pop_front();
    }

    ESP_LOGI(TAG, "📊 Emotion history size: %d", data_history_.emotion_history.size());

    // 检查是否需要触发警报
    if (data_history_.emotion_history.size() >= DataHistory::MAX_HISTORY_SIZE) {
        // 检查负面情绪
        if (ShouldTriggerAlert(data_history_.emotion_history, "Sad")) {
            ESP_LOGI(TAG, "🎯 Triggering Sad emotion alert");
            TriggerSmartAlert("emotion", "Sad");
        } else if (ShouldTriggerAlert(data_history_.emotion_history, "Angry")) {
            ESP_LOGI(TAG, "🎯 Triggering Angry emotion alert");
            TriggerSmartAlert("emotion", "Angry");
        } else if (ShouldTriggerAlert(data_history_.emotion_history, "Happy")) {
            ESP_LOGI(TAG, "🎯 Triggering Happy emotion alert");
            TriggerSmartAlert("emotion", "Happy");
        }
    }
}

void Application::ProcessPostureData(const std::string& posture) {
    // 添加新的坐姿数据到历史记录
    data_history_.posture_history.push_back(posture);

    // 保持历史记录大小不超过限制
    if (data_history_.posture_history.size() > DataHistory::MAX_HISTORY_SIZE) {
        data_history_.posture_history.pop_front();
    }

    ESP_LOGI(TAG, "📊 Posture history size: %d", data_history_.posture_history.size());

    // 检查是否需要触发警报
    if (data_history_.posture_history.size() >= DataHistory::MAX_HISTORY_SIZE) {
        if (ShouldTriggerAlert(data_history_.posture_history, "Poor Posture") ||
            ShouldTriggerAlert(data_history_.posture_history, "Mild Poor Posture") ||
            ShouldTriggerAlert(data_history_.posture_history, "Moderate Poor Posture") ||
            ShouldTriggerAlert(data_history_.posture_history, "Severe Poor Posture")) {
            TriggerSmartAlert("posture", "Poor");
        } else if (ShouldTriggerAlert(data_history_.posture_history, "Good Posture")) {
            TriggerSmartAlert("posture", "Good");
        }
    }
}

bool Application::ShouldTriggerAlert(const std::deque<std::string>& history, const std::string& target_state) {
    if (history.size() < DataHistory::MAX_HISTORY_SIZE) {
        return false;
    }

    // 计算目标状态在最近5次数据中出现的次数
    int count = 0;
    for (const auto& state : history) {
        if (state == target_state ||
            (target_state == "Poor" && (state == "Poor Posture" ||
                                       state == "Mild Poor Posture" ||
                                       state == "Moderate Poor Posture" ||
                                       state == "Severe Poor Posture"))) {
            count++;
        }
    }

    ESP_LOGI(TAG, "📊 State '%s' appears %d times in last %d records",
             target_state.c_str(), count, DataHistory::MAX_HISTORY_SIZE);

    return count >= DataHistory::ALERT_THRESHOLD;
}

void Application::TriggerSmartAlert(const std::string& type, const std::string& state) {
    uint32_t current_time = esp_timer_get_time() / 1000; // 转换为毫秒

    // 检查设备状态 - 如果设备忙碌，强制唤醒进行紧急警报
    if (device_state_ != kDeviceStateIdle) {
        ESP_LOGI(TAG, "🚨 Device busy, forcing wake up for critical alert (>4 times)");
        // 强制停止当前活动并回到空闲状态
        if (device_state_ == kDeviceStateSpeaking) {
            AbortSpeaking(kAbortReasonNone);
        } else if (device_state_ == kDeviceStateListening) {
            StopListening();
        }
        SetDeviceState(kDeviceStateIdle);
    }

    // 检查冷却时间
    uint32_t time_since_last_alert = current_time - data_history_.last_alert_time;
    if (time_since_last_alert < DataHistory::ALERT_COOLDOWN_MS) {
        uint32_t remaining_cooldown = DataHistory::ALERT_COOLDOWN_MS - time_since_last_alert;
        ESP_LOGI(TAG, "⏰ Alert cooldown active, skipping alert. Remaining: %u ms", remaining_cooldown);
        return;
    }

    // 检查是否是重复的警报
    std::string alert_key = type + "_" + state;
    if ((type == "emotion" && data_history_.last_emotion_alert == state) ||
        (type == "posture" && data_history_.last_posture_alert == state)) {
        ESP_LOGI(TAG, "🔄 Duplicate alert for %s:%s, skipping", type.c_str(), state.c_str());
        return;
    }

    // 更新警报状态
    data_history_.last_alert_time = current_time;
    if (type == "emotion") {
        data_history_.last_emotion_alert = state;
    } else if (type == "posture") {
        data_history_.last_posture_alert = state;
    }

    // 生成警报消息和表情
    std::string message;
    std::string emotion;

    if (type == "emotion") {
        if (state == "Sad") {
            message = "我注意到你最近看起来有些难过，要不要聊聊天？";
            emotion = "sad";
        } else if (state == "Angry") {
            message = "你看起来有点生气，深呼吸一下，放松心情吧。";
            emotion = "angry";
        } else if (state == "Happy") {
            message = "你看起来心情很好呢，真棒！";
            emotion = "happy";
        }
    } else if (type == "posture") {
        if (state == "Poor") {
            message = "我发现你的坐姿不太好，记得挺直腰背哦！";
            emotion = "neutral";
        } else if (state == "Good") {
            message = "你的坐姿很端正，继续保持！";
            emotion = "happy";
        }
    }

    if (!message.empty()) {
        ESP_LOGI(TAG, "🚨 Critical Smart Alert (>4 times): %s", message.c_str());

        Schedule([this, message, emotion]() {
            // 1. 强制唤醒设备
            ESP_LOGI(TAG, "🎯 Force waking up device for critical alert");
            ForceWakeUpForAlert();

            // 2. 设置显示信息
            auto display = Board::GetInstance().GetDisplay();
            display->SetChatMessage("assistant", message.c_str());
            display->SetStatus("紧急提醒");
            display->SetEmotion(emotion.c_str());

            // 3. 播放唤醒音效（而不是震动声音）
            ESP_LOGI(TAG, "🔊 Playing wake-up sound for critical alert");
            ResetDecoder();
            PlaySound(Lang::Sounds::P3_POPUP);

            // 4. 等待音效播放完成，然后发送TTS请求
            vTaskDelay(pdMS_TO_TICKS(500));

            // 5. 发送TTS请求进行语音播报
            ESP_LOGI(TAG, "🗣️ Sending TTS request for voice alert");
            if (protocol_) {
                // 直接使用Protocol类的SendTtsRequest方法
                protocol_->SendTtsRequest(message.c_str());
                ESP_LOGI(TAG, "✅ TTS request sent for critical alert");
            } else {
                ESP_LOGW(TAG, "⚠️ Protocol not available, cannot send TTS request");
                // 如果没有协议连接，至少播放一个更明显的音效
                PlaySoundImmediate(Lang::Sounds::P3_ACTIVATION);
            }

            ESP_LOGI(TAG, "✅ Critical alert processed with voice synthesis request");
        });
    }
}

void Application::ForceWakeUpForAlert() {
    ESP_LOGI(TAG, "🚨 Force wake up for critical alert");

    // 停止当前所有活动
    if (device_state_ == kDeviceStateSpeaking) {
        AbortSpeaking(kAbortReasonNone);
    } else if (device_state_ == kDeviceStateListening) {
        StopListening();
    }

    // 确保音频输出启用
    auto codec = Board::GetInstance().GetAudioCodec();
    if (codec && !codec->output_enabled()) {
        ESP_LOGI(TAG, "🔊 Enabling audio output for critical alert");
        codec->EnableOutput(true);
    }

    // 设置为空闲状态，准备接收语音
    SetDeviceState(kDeviceStateIdle);
    is_critical_alert_active_ = true;

    ESP_LOGI(TAG, "✅ Device ready for critical alert voice playback");
}

std::string Application::GetCurrentEmotionStatus() {
    if (data_history_.emotion_history.empty()) {
        return "暂时没有检测到表情数据";
    }

    // 获取最新的表情状态
    std::string latest_emotion = data_history_.emotion_history.back();

    // 统计最近5次表情数据
    std::map<std::string, int> emotion_count;
    for (const auto& emotion : data_history_.emotion_history) {
        emotion_count[emotion]++;
    }

    // 构建状态报告
    std::string report = "当前表情状态：" + latest_emotion + "\n";
    report += "最近" + std::to_string(data_history_.emotion_history.size()) + "次检测结果：\n";

    for (const auto& pair : emotion_count) {
        report += "- " + pair.first + ": " + std::to_string(pair.second) + "次\n";
    }

    // 添加趋势分析
    if (emotion_count["Sad"] >= 3) {
        report += "\n😔 检测到你最近情绪有些低落";
    } else if (emotion_count["Happy"] >= 3) {
        report += "\n😊 你最近心情很不错呢！";
    } else if (emotion_count["Angry"] >= 3) {
        report += "\n😠 你最近看起来有些烦躁";
    } else {
        report += "\n😌 你的情绪状态比较平稳";
    }

    return report;
}

std::string Application::GetCurrentPostureStatus() {
    if (data_history_.posture_history.empty()) {
        return "暂时没有检测到坐姿数据";
    }

    // 获取最新的坐姿状态
    std::string latest_posture = data_history_.posture_history.back();

    // 统计最近5次坐姿数据
    std::map<std::string, int> posture_count;
    for (const auto& posture : data_history_.posture_history) {
        posture_count[posture]++;
    }

    // 构建状态报告
    std::string report = "当前坐姿状态：" + latest_posture + "\n";
    report += "最近" + std::to_string(data_history_.posture_history.size()) + "次检测结果：\n";

    for (const auto& pair : posture_count) {
        report += "- " + pair.first + ": " + std::to_string(pair.second) + "次\n";
    }

    // 计算不良坐姿次数
    int poor_posture_count = posture_count["Poor Posture"] +
                           posture_count["Mild Poor Posture"] +
                           posture_count["Moderate Poor Posture"] +
                           posture_count["Severe Poor Posture"];

    // 添加健康建议
    if (poor_posture_count >= 3) {
        report += "\n⚠️ 你的坐姿需要改善，建议：\n";
        report += "- 保持背部挺直\n";
        report += "- 双脚平放在地面\n";
        report += "- 定期起身活动";
    } else if (posture_count["Good Posture"] >= 3) {
        report += "\n✅ 你的坐姿很棒，继续保持！";
    } else {
        report += "\n💡 注意保持良好的坐姿习惯";
    }

    return report;
}

void Application::InitializeBleClient() {
    ESP_LOGI(TAG, "Initializing BLE client");

    // 获取BLE客户端实例
    ble_client_ = BleClient::GetInstance();
    if (!ble_client_) {
        ESP_LOGE(TAG, "Failed to get BLE client instance");
        return;
    }

    // 设置设备名称
    ble_client_->SetDeviceName("XiaoZhi-PICO-Client");

    // 设置数据接收回调
    ble_client_->SetDataCallback([this](const uint8_t* data, size_t length) {
        this->OnBleDataReceived(data, length);
    });

    // 设置连接状态回调
    ble_client_->SetConnectionCallback([this](BleConnectionState state, const esp_bd_addr_t& address) {
        this->OnBleConnectionChanged(state, address);
    });

    // 初始化BLE客户端
    if (!ble_client_->Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize BLE client");
        BleClient::DestroyInstance();
        ble_client_ = nullptr;
        return;
    }

    ble_client_initialized_ = true;
    ESP_LOGI(TAG, "BLE client initialized successfully");

    // 开始搜索目标设备
    StartBleDeviceSearch();
}

void Application::OnBleDataReceived(const uint8_t* data, size_t length) {
    ESP_LOGI(TAG, "BLE data received: %zu bytes", length);

    // 将BLE数据作为字符串处理
    std::string data_str(reinterpret_cast<const char*>(data), length);
    ESP_LOGI(TAG, "BLE data content: %s", data_str.c_str());

    // 处理BLE命令
    if (data_str == "LED_ON") {
        ESP_LOGI(TAG, "Command: Turn LED ON");
        // 这里添加LED控制代码
    } else if (data_str == "LED_OFF") {
        ESP_LOGI(TAG, "Command: Turn LED OFF");
        // 这里添加LED控制代码
    } else if (data_str.find("PLAY:") == 0) {
        std::string filename = data_str.substr(5);
        ESP_LOGI(TAG, "Command: Play audio file: %s", filename.c_str());
        // 这里添加音频播放代码
    } else if (data_str == "STATUS") {
        ESP_LOGI(TAG, "Command: Get device status");
        // 发送状态信息回手机
        if (ble_client_ && ble_connected_) {
            std::string status = "Device OK, Free RAM: " + std::to_string(esp_get_free_heap_size());
            ble_client_->SendData((const uint8_t*)status.c_str(), status.length());
        }
    } else {
        ESP_LOGI(TAG, "Unknown command: %s", data_str.c_str());
    }
}

void Application::StartBleDeviceSearch() {
    ESP_LOGI(TAG, "Starting BLE device search for DevKitC...");

    if (!ble_client_) {
        ESP_LOGE(TAG, "BLE client not initialized");
        return;
    }

    // 开始扫描设备
    if (!ble_client_->StartScan(10)) {
        ESP_LOGE(TAG, "Failed to start BLE scan");
        return;
    }

    ESP_LOGI(TAG, "BLE scan started, looking for target device...");
}

void Application::ConnectToTargetDevice() {
    if (!ble_client_) {
        ESP_LOGE(TAG, "BLE client not initialized");
        return;
    }

    auto devices = ble_client_->GetDiscoveredDevices();
    ESP_LOGI(TAG, "Found %d BLE devices", devices.size());

    // 查找目标设备 (DevKitC)
    for (const auto& device : devices) {
        std::string addr_str = BleManager::BdAddrToString(device.address);
        ESP_LOGI(TAG, "Device: %s, Name: %s, RSSI: %d",
                 addr_str.c_str(), device.name.c_str(), device.rssi);

        // 这里可以根据设备名称或地址来识别DevKitC
        // 假设DevKitC的设备名称包含"DevKitC"或"XiaoZhi"
        if (device.name.find("XiaoZhi") != std::string::npos ||
            device.name.find("DevKitC") != std::string::npos ||
            device.name.find("ESP32") != std::string::npos) {

            ESP_LOGI(TAG, "Found target device: %s", addr_str.c_str());
            memcpy(target_device_address_, device.address, sizeof(esp_bd_addr_t));

            // 连接到目标设备
            if (ble_client_->Connect(device.address)) {
                ESP_LOGI(TAG, "Connecting to target device...");
                return;
            } else {
                ESP_LOGE(TAG, "Failed to initiate connection");
            }
        }
    }

    ESP_LOGW(TAG, "Target device not found, will retry scan in 5 seconds");
    // 5秒后重新扫描
    vTaskDelay(pdMS_TO_TICKS(5000));
    StartBleDeviceSearch();
}

bool Application::SendDataViaBle(const uint8_t* data, size_t length) {
    if (!ble_client_ || !ble_connected_) {
        ESP_LOGD(TAG, "BLE not connected, cannot send data");
        return false;
    }

    ESP_LOGI(TAG, "Sending %zu bytes via BLE", length);
    return ble_client_->SendData(data, length);
}

void Application::OnBleConnectionChanged(BleConnectionState state, const esp_bd_addr_t& address) {
    std::string addr_str = BleManager::BdAddrToString(address);
    ESP_LOGI(TAG, "BLE connection state changed: %s, device: %s",
             BleManager::ConnectionStateToString(state).c_str(), addr_str.c_str());

    auto& app = Application::GetInstance();

    switch (state) {
        case BLE_CONNECTED:
            app.ble_connected_ = true;
            ESP_LOGI(TAG, "✅ Connected to DevKitC successfully!");
            break;
        case BLE_DISCONNECTED:
            // 检查是否是扫描完成的特殊通知（地址全为0）
            if (memcmp(address, "\x00\x00\x00\x00\x00\x00", 6) == 0) {
                ESP_LOGI(TAG, "📡 BLE scan completed, attempting to connect...");
                app.ConnectToTargetDevice();
            } else {
                app.ble_connected_ = false;
                ESP_LOGI(TAG, "❌ Disconnected from DevKitC, will retry connection...");
                // 重新开始搜索和连接
                vTaskDelay(pdMS_TO_TICKS(2000));
                app.StartBleDeviceSearch();
            }
            break;
        case BLE_CONNECTING:
            ESP_LOGI(TAG, "🔄 Connecting to DevKitC...");
            break;
        default:
            break;
    }

    auto display = Board::GetInstance().GetDisplay();
    if (display) {
        switch (state) {
            case BLE_CONNECTED:
                display->ShowNotification("已连接到DevKitC", 3000);
                break;
            case BLE_DISCONNECTED:
                display->ShowNotification("DevKitC连接断开", 3000);
                break;
            default:
                break;
        }
    }
}
