#ifndef DEVKITC_BLUETOOTH_CLIENT_H
#define DEVKITC_BLUETOOTH_CLIENT_H

#include "esp_gap_bt_api.h"
#include "esp_spp_api.h"
#include "esp_bt_device.h"
#include <string>
#include <functional>
#include <vector>

/**
 * @brief ESP32-S3 DevKitC-1 蓝牙客户端类
 * 
 * 用于连接ESP32-S3 PICO-1并接收转发的WiFi数据
 */
class DevKitCBluetoothClient {
public:
    // 回调函数类型定义
    typedef std::function<void(const uint8_t* data, size_t length)> data_received_callback_t;
    typedef std::function<void(bool connected, const std::string& device_name)> connection_status_callback_t;
    typedef std::function<void(const std::string& message)> status_callback_t;
    
    // 设备发现结构体
    struct DiscoveredDevice {
        esp_bd_addr_t address;
        std::string name;
        int rssi;
        uint32_t cod;  // Class of Device
    };

private:
    // 连接状态
    enum ConnectionState {
        DISCONNECTED,
        DISCOVERING,
        CONNECTING,
        CONNECTED
    };
    
    ConnectionState connection_state_;
    bool initialized_;
    bool discovering_;
    uint32_t spp_handle_;
    
    // 目标设备信息
    std::string target_device_name_;
    esp_bd_addr_t target_address_;
    bool target_found_;
    
    // 发现的设备列表
    std::vector<DiscoveredDevice> discovered_devices_;
    
    // 回调函数
    data_received_callback_t data_callback_;
    connection_status_callback_t connection_callback_;
    status_callback_t status_callback_;
    
    // 连接参数
    int discovery_timeout_sec_;
    int connection_timeout_sec_;
    bool auto_reconnect_;
    
    // 统计信息
    uint32_t packets_received_;
    uint32_t bytes_received_;
    uint32_t connection_attempts_;

public:
    /**
     * @brief 构造函数
     * 
     * @param target_device_name 目标设备名称（PICO-1的蓝牙名称）
     */
    DevKitCBluetoothClient(const std::string& target_device_name = "ESP32-S3-PICO-Bridge");
    
    /**
     * @brief 析构函数
     */
    ~DevKitCBluetoothClient();
    
    /**
     * @brief 初始化蓝牙客户端
     * 
     * @return true 成功
     * @return false 失败
     */
    bool Initialize();
    
    /**
     * @brief 反初始化蓝牙客户端
     */
    void Deinitialize();
    
    /**
     * @brief 开始发现设备
     * 
     * @param timeout_sec 发现超时时间（秒）
     * @return true 成功
     * @return false 失败
     */
    bool StartDiscovery(int timeout_sec = 30);
    
    /**
     * @brief 停止发现设备
     * 
     * @return true 成功
     * @return false 失败
     */
    bool StopDiscovery();
    
    /**
     * @brief 连接到目标设备
     * 
     * @return true 成功
     * @return false 失败
     */
    bool ConnectToTarget();
    
    /**
     * @brief 连接到指定设备
     * 
     * @param address 设备地址
     * @return true 成功
     * @return false 失败
     */
    bool ConnectToDevice(const esp_bd_addr_t& address);
    
    /**
     * @brief 断开连接
     * 
     * @return true 成功
     * @return false 失败
     */
    bool Disconnect();
    
    /**
     * @brief 发送数据到服务器
     * 
     * @param data 数据
     * @param length 数据长度
     * @return true 成功
     * @return false 失败
     */
    bool SendData(const uint8_t* data, size_t length);
    
    /**
     * @brief 发送字符串到服务器
     * 
     * @param message 消息
     * @return true 成功
     * @return false 失败
     */
    bool SendString(const std::string& message);
    
    /**
     * @brief 设置数据接收回调
     * 
     * @param callback 回调函数
     */
    void SetDataReceivedCallback(data_received_callback_t callback);
    
    /**
     * @brief 设置连接状态回调
     * 
     * @param callback 回调函数
     */
    void SetConnectionStatusCallback(connection_status_callback_t callback);
    
    /**
     * @brief 设置状态消息回调
     * 
     * @param callback 回调函数
     */
    void SetStatusCallback(status_callback_t callback);
    
    /**
     * @brief 设置目标设备名称
     * 
     * @param name 设备名称
     */
    void SetTargetDeviceName(const std::string& name);
    
    /**
     * @brief 设置自动重连
     * 
     * @param enable 是否启用自动重连
     */
    void SetAutoReconnect(bool enable);
    
    /**
     * @brief 获取连接状态
     * 
     * @return true 已连接
     * @return false 未连接
     */
    bool IsConnected() const { return connection_state_ == CONNECTED; }
    
    /**
     * @brief 获取发现状态
     * 
     * @return true 正在发现
     * @return false 未在发现
     */
    bool IsDiscovering() const { return discovering_; }
    
    /**
     * @brief 获取发现的设备列表
     * 
     * @return const std::vector<DiscoveredDevice>& 设备列表
     */
    const std::vector<DiscoveredDevice>& GetDiscoveredDevices() const;
    
    /**
     * @brief 获取统计信息
     * 
     * @param packets_received 接收包数
     * @param bytes_received 接收字节数
     * @param connection_attempts 连接尝试次数
     */
    void GetStatistics(uint32_t& packets_received, uint32_t& bytes_received, uint32_t& connection_attempts) const;
    
    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

private:
    /**
     * @brief 初始化蓝牙控制器
     * 
     * @return true 成功
     * @return false 失败
     */
    bool InitializeController();
    
    /**
     * @brief 初始化GAP
     * 
     * @return true 成功
     * @return false 失败
     */
    bool InitializeGap();
    
    /**
     * @brief 初始化SPP
     * 
     * @return true 成功
     * @return false 失败
     */
    bool InitializeSpp();
    
    /**
     * @brief GAP事件处理
     * 
     * @param event 事件类型
     * @param param 事件参数
     */
    void HandleGapEvent(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param);
    
    /**
     * @brief SPP事件处理
     * 
     * @param event 事件类型
     * @param param 事件参数
     */
    void HandleSppEvent(esp_spp_cb_event_t event, esp_spp_cb_param_t* param);
    
    /**
     * @brief 处理设备发现
     * 
     * @param param 发现参数
     */
    void HandleDeviceDiscovered(esp_bt_gap_cb_param_t* param);
    
    /**
     * @brief 处理连接建立
     * 
     * @param handle SPP句柄
     */
    void HandleConnectionEstablished(uint32_t handle);
    
    /**
     * @brief 处理连接断开
     */
    void HandleConnectionClosed();
    
    /**
     * @brief 处理接收到的数据
     * 
     * @param data 数据
     * @param length 数据长度
     */
    void HandleDataReceived(const uint8_t* data, size_t length);
    
    /**
     * @brief 发送状态消息
     * 
     * @param message 消息内容
     */
    void SendStatusMessage(const std::string& message);
    
    /**
     * @brief 静态GAP回调函数
     * 
     * @param event 事件类型
     * @param param 事件参数
     */
    static void StaticGapCallback(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param);
    
    /**
     * @brief 静态SPP回调函数
     * 
     * @param event 事件类型
     * @param param 事件参数
     */
    static void StaticSppCallback(esp_spp_cb_event_t event, esp_spp_cb_param_t* param);
    
    static DevKitCBluetoothClient* instance_;
};

#endif // DEVKITC_BLUETOOTH_CLIENT_H
