#!/usr/bin/env python3
"""
小智智能提醒功能测试脚本
测试5次数据中3次以上触发提醒的功能
"""

import socket
import json
import time
import random

# 小智设备配置
XIAOZHI_IP = "**************"  # 根据实际情况修改
XIAOZHI_PORT = 8080

def send_data(emotion, posture, confidence=0.85):
    """发送单条数据到小智"""
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect((XIAOZHI_IP, XIAOZHI_PORT))
        
        # 构造数据
        data = {
            "emotion": emotion,
            "posture": posture,
            "confidence": confidence,
            "timestamp": int(time.time() * 1000)
        }
        
        # 发送JSON数据
        json_data = json.dumps(data)
        sock.send(json_data.encode('utf-8'))
        
        print(f"✅ 发送数据: {json_data}")
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ 发送失败: {e}")
        return False

def test_emotion_alerts():
    """测试表情智能提醒"""
    print("\n🎭 测试表情智能提醒功能")
    print("=" * 50)
    
    # 测试场景1: 连续5次Sad，应该在第5次时触发提醒
    print("\n📋 场景1: 连续5次Sad表情")
    for i in range(5):
        print(f"第{i+1}次发送...")
        send_data("Sad", "Good Posture")
        time.sleep(2)
    
    print("\n⏰ 等待10秒观察是否有语音提醒...")
    time.sleep(10)
    
    # 测试场景2: 混合数据，Angry出现3次
    print("\n📋 场景2: 混合数据中Angry出现3次")
    emotions = ["Happy", "Angry", "Calm", "Angry", "Angry"]
    for i, emotion in enumerate(emotions):
        print(f"第{i+1}次发送: {emotion}")
        send_data(emotion, "Good Posture")
        time.sleep(2)
    
    print("\n⏰ 等待10秒观察是否有语音提醒...")
    time.sleep(10)

def test_posture_alerts():
    """测试坐姿智能提醒"""
    print("\n🪑 测试坐姿智能提醒功能")
    print("=" * 50)

    # 测试场景3: 连续5次Moderate Poor Posture（新增类型）
    print("\n📋 场景3: 连续5次Moderate Poor Posture")
    for i in range(5):
        print(f"第{i+1}次发送...")
        send_data("Calm", "Moderate Poor Posture")
        time.sleep(2)

    print("\n⏰ 等待10秒观察是否有语音提醒...")
    time.sleep(10)

    # 测试场景4: 混合坐姿数据，包含新类型
    print("\n📋 场景4: 混合坐姿数据（包含新类型）")
    postures = ["Good Posture", "Severe Poor Posture", "Good Posture", "Moderate Poor Posture", "Severe Poor Posture"]
    for i, posture in enumerate(postures):
        print(f"第{i+1}次发送: {posture}")
        send_data("Calm", posture)
        time.sleep(2)

    print("\n⏰ 等待10秒观察是否有语音提醒...")
    time.sleep(10)

def test_cooldown_mechanism():
    """测试冷却机制"""
    print("\n⏰ 测试冷却机制")
    print("=" * 50)
    
    # 先触发一次提醒
    print("\n📋 第一次触发提醒")
    for i in range(5):
        send_data("Sad", "Good Posture")
        time.sleep(1)
    
    print("\n⏰ 等待5秒...")
    time.sleep(5)
    
    # 立即再次尝试触发（应该被冷却机制阻止）
    print("\n📋 立即再次尝试触发（应该被冷却）")
    for i in range(5):
        send_data("Sad", "Good Posture")
        time.sleep(1)
    
    print("\n⏰ 等待5秒观察是否被冷却...")
    time.sleep(5)

def test_mixed_scenarios():
    """测试混合场景"""
    print("\n🔄 测试混合场景")
    print("=" * 50)
    
    # 模拟真实使用场景
    scenarios = [
        ("Happy", "Good Posture"),
        ("Calm", "Good Posture"),
        ("Sad", "Poor Posture"),
        ("Sad", "Poor Posture"),
        ("Angry", "Mild Poor Posture"),
        ("Sad", "Poor Posture"),
        ("Calm", "Good Posture"),
        ("Happy", "Good Posture"),
    ]
    
    print("\n📋 模拟真实使用场景")
    for i, (emotion, posture) in enumerate(scenarios):
        print(f"第{i+1}次: {emotion} + {posture}")
        send_data(emotion, posture)
        time.sleep(3)
    
    print("\n⏰ 等待10秒观察最终结果...")
    time.sleep(10)

def main():
    """主函数"""
    print("🤖 小智智能提醒功能测试")
    print("=" * 60)
    print(f"目标设备: {XIAOZHI_IP}:{XIAOZHI_PORT}")
    print("测试规则: 5次数据中出现3次以上相同状态时触发提醒")
    print("冷却时间: 30秒")
    
    try:
        # 测试连接
        print("\n🔗 测试连接...")
        if not send_data("Happy", "Good Posture"):
            print("❌ 无法连接到小智设备，请检查IP地址和端口")
            return
        
        print("✅ 连接成功！开始测试...")
        time.sleep(3)
        
        # 运行各种测试
        test_emotion_alerts()
        test_posture_alerts()
        test_cooldown_mechanism()
        test_mixed_scenarios()
        
        print("\n🎉 所有测试完成！")
        print("请观察小智的语音反馈和显示屏消息。")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
