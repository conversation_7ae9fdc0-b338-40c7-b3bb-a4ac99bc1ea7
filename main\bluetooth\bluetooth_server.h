#ifndef BLUETOOTH_SERVER_H
#define BLUETOOTH_SERVER_H

#include "bluetooth_manager.h"
#include "esp_gap_bt_api.h"
#include "esp_spp_api.h"
#include <functional>
#include <vector>

// 数据传输回调函数类型
typedef std::function<void(const uint8_t* data, size_t length)> data_received_callback_t;
typedef std::function<void(bool connected, const esp_bd_addr_t& address)> connection_status_callback_t;

// 蓝牙服务器类 - 用于ESP32-S3-PICO
class BluetoothServer : public BluetoothManager {
private:
    static const char* SERVER_NAME;
    static const char* SPP_SERVICE_NAME;
    static const esp_spp_sec_t SPP_SEC_MASK;
    static const esp_spp_role_t SPP_ROLE;

    bool server_started_;
    bool discoverable_;
    bool connectable_;
    uint32_t spp_handle_;
    bool client_connected_;
    esp_bd_addr_t connected_device_;

    // 服务器配置
    std::string device_name_;
    esp_bt_pin_code_t pin_code_;
    bool use_pin_code_;

    // 回调函数
    data_received_callback_t data_callback_;
    connection_status_callback_t connection_callback_;

public:
    BluetoothServer(const std::string& device_name = "ESP32-S3-PICO-Server");
    virtual ~BluetoothServer();

    // 重写基类方法
    virtual bool Initialize() override;
    virtual bool StartDiscovery() override;
    virtual bool StopDiscovery() override;
    virtual bool Connect(const esp_bd_addr_t& address) override;

    // 服务器特有方法
    bool StartServer();
    bool StopServer();
    bool SetDiscoverable(bool discoverable);
    bool SetConnectable(bool connectable);

    // 数据传输方法
    bool SendData(const uint8_t* data, size_t length);
    bool SendString(const std::string& message);

    // 回调设置
    void SetDataReceivedCallback(data_received_callback_t callback);
    void SetConnectionStatusCallback(connection_status_callback_t callback);

    // 配置方法
    void SetDeviceName(const std::string& name);
    void SetPinCode(const std::string& pin);
    void DisablePinCode();

    // 状态查询
    bool IsServerStarted() const { return server_started_; }
    bool IsDiscoverable() const { return discoverable_; }
    bool IsConnectable() const { return connectable_; }
    bool IsClientConnected() const { return client_connected_; }
    esp_bd_addr_t GetConnectedDevice() const { return connected_device_; }

protected:
    // 内部回调处理
    void HandleGapEvent(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param);
    void HandleSppEvent(esp_spp_cb_event_t event, esp_spp_cb_param_t* param);

private:
    // 初始化相关
    bool InitializeGap();
    bool InitializeSpp();
    bool SetDeviceProperties();

    // 连接管理
    void OnClientConnected(uint32_t handle, const esp_bd_addr_t& address);
    void OnClientDisconnected();
    void OnDataReceived(const uint8_t* data, size_t length);

    // 静态回调函数
    static void StaticGapCallback(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param);
    static void StaticSppCallback(esp_spp_cb_event_t event, esp_spp_cb_param_t* param);

    static BluetoothServer* instance_;
};

#endif // BLUETOOTH_SERVER_H
