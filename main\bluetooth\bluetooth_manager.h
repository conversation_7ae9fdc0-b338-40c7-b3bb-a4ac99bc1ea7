#ifndef BLUETOOTH_MANAGER_H
#define BLUETOOTH_MANAGER_H

#include <string>
#include <functional>
#include <memory>
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_gap_bt_api.h"
#include "esp_spp_api.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

// 蓝牙连接状态枚举
enum BluetoothConnectionState {
    BT_DISCONNECTED = 0,
    BT_CONNECTING,
    BT_CONNECTED,
    BT_DISCONNECTING
};

// 蓝牙设备信息结构体
struct BluetoothDeviceInfo {
    esp_bd_addr_t address;
    std::string name;
    int rssi;
    uint32_t cod;  // Class of Device
};

// 蓝牙数据包结构体
struct BluetoothDataPacket {
    uint8_t* data;
    size_t length;
    uint32_t handle;
};

// 蓝牙管理器基类
class BluetoothManager {
public:
    // 回调函数类型定义
    using ConnectionStateCallback = std::function<void(BluetoothConnectionState state, const std::string& device_name)>;
    using DataReceivedCallback = std::function<void(const uint8_t* data, size_t length)>;
    using DeviceDiscoveredCallback = std::function<void(const BluetoothDeviceInfo& device)>;
    using ErrorCallback = std::function<void(const std::string& error_message)>;

protected:
    static const char* TAG;
    
    // 连接状态
    BluetoothConnectionState connection_state_;
    std::string connected_device_name_;
    esp_bd_addr_t connected_device_address_;
    
    // SPP相关
    uint32_t spp_handle_;
    bool spp_connected_;
    
    // 回调函数
    ConnectionStateCallback connection_callback_;
    DataReceivedCallback data_callback_;
    DeviceDiscoveredCallback discovery_callback_;
    ErrorCallback error_callback_;
    
    // 数据队列
    QueueHandle_t tx_queue_;
    QueueHandle_t rx_queue_;
    
    // 任务句柄
    TaskHandle_t tx_task_handle_;
    TaskHandle_t rx_task_handle_;

public:
    BluetoothManager();
    virtual ~BluetoothManager();
    
    // 基础功能
    virtual bool Initialize() = 0;
    virtual bool Deinitialize();
    virtual bool IsInitialized() const;
    
    // 连接管理
    virtual bool StartDiscovery() = 0;
    virtual bool StopDiscovery() = 0;
    virtual bool Connect(const esp_bd_addr_t& address) = 0;
    virtual bool Disconnect();
    
    // 数据传输
    virtual bool SendData(const uint8_t* data, size_t length);
    virtual bool SendString(const std::string& message);
    
    // 状态查询
    BluetoothConnectionState GetConnectionState() const { return connection_state_; }
    bool IsConnected() const { return connection_state_ == BT_CONNECTED; }
    std::string GetConnectedDeviceName() const { return connected_device_name_; }
    
    // 回调设置
    void SetConnectionStateCallback(ConnectionStateCallback callback) { connection_callback_ = callback; }
    void SetDataReceivedCallback(DataReceivedCallback callback) { data_callback_ = callback; }
    void SetDeviceDiscoveredCallback(DeviceDiscoveredCallback callback) { discovery_callback_ = callback; }
    void SetErrorCallback(ErrorCallback callback) { error_callback_ = callback; }

protected:
    // 内部方法
    virtual void OnConnectionStateChanged(BluetoothConnectionState state, const std::string& device_name);
    virtual void OnDataReceived(const uint8_t* data, size_t length);
    virtual void OnDeviceDiscovered(const BluetoothDeviceInfo& device);
    virtual void OnError(const std::string& error_message);
    
    // 静态回调函数
    static void GapCallback(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param);
    static void SppCallback(esp_spp_cb_event_t event, esp_spp_cb_param_t* param);
    
    // 任务函数
    static void TxTask(void* parameter);
    static void RxTask(void* parameter);
    
    // 工具函数
    static std::string BdAddrToString(const esp_bd_addr_t& addr);
    static void StringToBdAddr(const std::string& addr_str, esp_bd_addr_t& addr);
    static std::string ConnectionStateToString(BluetoothConnectionState state);

protected:
    void SetInitialized(bool initialized) { initialized_ = initialized; }

private:
    static BluetoothManager* instance_;
    bool initialized_;
};

#endif // BLUETOOTH_MANAGER_H
