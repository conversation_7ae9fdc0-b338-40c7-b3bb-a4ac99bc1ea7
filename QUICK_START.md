# ESP32-S3 蓝牙WiFi桥接器 - 快速开始

## 概述

这个项目实现了ESP32-S3 PICO-1作为WiFi到蓝牙的桥接器，将WiFi接收的数据通过蓝牙转发给ESP32-S3 DevKitC-1。

## 快速设置

### 第一步：准备硬件
- ESP32-S3-PICO-1 开发板 × 1
- ESP32-S3-DevKitC-1 开发板 × 1
- USB-C 数据线 × 2

### 第二步：配置PICO-1 (桥接器)

1. **修改WiFi配置**
   编辑 `main/pico_wifi_bt_main.cc`：
   ```cpp
   #define WIFI_SSID      "你的WiFi名称"
   #define WIFI_PASS      "你的WiFi密码"
   ```

2. **编译和烧录**
   ```bash
   # 设置目标芯片
   idf.py set-target esp32s3
   
   # 配置项目（启用蓝牙）
   idf.py menuconfig
   
   # 编译
   idf.py build
   
   # 烧录
   idf.py -p COM_PORT flash monitor
   ```

3. **确认启动成功**
   查看串口输出，应该看到：
   ```
   I (xxx) PICO_MAIN: WiFi connected. IP: 192.168.1.xxx
   I (xxx) PICO_MAIN: WiFi Server listening on 192.168.1.xxx:8080
   I (xxx) PICO_MAIN: Bluetooth device name: ESP32-S3-PICO-Bridge
   ```

### 第三步：配置DevKitC-1 (接收器)

1. **创建新项目**
   ```bash
   mkdir devkitc_project
   cd devkitc_project
   idf.py create-project devkitc_bt_client
   ```

2. **复制文件**
   将以下文件复制到项目的 `main` 目录：
   - `devkitc_bluetooth_client.h`
   - `devkitc_bluetooth_client.cc`
   - `devkitc_main.cc`

3. **修改CMakeLists.txt**
   编辑 `main/CMakeLists.txt`：
   ```cmake
   idf_component_register(SRCS "devkitc_main.cc" "devkitc_bluetooth_client.cc"
                         INCLUDE_DIRS ".")
   ```

4. **编译和烧录**
   ```bash
   idf.py set-target esp32s3
   idf.py menuconfig  # 启用蓝牙
   idf.py build
   idf.py -p COM_PORT flash monitor
   ```

5. **确认连接成功**
   查看串口输出，应该看到：
   ```
   I (xxx) DevKitC_Main: Target device found: ESP32-S3-PICO-Bridge
   I (xxx) DevKitC_Main: Connected to device: ESP32-S3-PICO-Bridge
   I (xxx) DevKitC_Main: Ready to receive data
   ```

### 第四步：测试数据传输

1. **获取PICO-1的IP地址**
   从PICO-1的串口输出中找到IP地址，例如：`*************`

2. **发送测试数据**
   
   **方法1：使用Python脚本**
   ```bash
   python test_wifi_sender.py ************* -m single -t "Hello World"
   ```
   
   **方法2：使用curl**
   ```bash
   curl -X POST -d "Temperature: 25.5°C" http://*************:8080
   ```
   
   **方法3：使用netcat**
   ```bash
   echo "Sensor data" | nc ************* 8080
   ```

3. **验证数据接收**
   在DevKitC-1的串口输出中应该看到：
   ```
   I (xxx) DevKitC_Main: Data received: xx bytes
   I (xxx) DevKitC_Main: Received data: [WiFi:*************:xxxxx] Hello World
   ```

## 常见问题解决

### 问题1：WiFi连接失败
**解决方案：**
- 检查WiFi SSID和密码是否正确
- 确认WiFi是2.4GHz频段
- 检查WiFi信号强度

### 问题2：蓝牙连接失败
**解决方案：**
- 确认两个设备都启用了蓝牙
- 检查设备名称是否匹配
- 重启两个设备

### 问题3：数据传输失败
**解决方案：**
- 确认PICO-1的IP地址正确
- 检查端口8080是否被占用
- 验证蓝牙连接状态

## 进阶使用

### 连续数据测试
```bash
# 每2秒发送一次传感器数据
python test_wifi_sender.py ************* -m continuous -i 2.0

# 发送10条批次数据
python test_wifi_sender.py ************* -m batch -c 10
```

### 自定义数据处理
在 `devkitc_main.cc` 的 `data_processing_task` 函数中添加你的数据处理逻辑：

```cpp
// 在data_processing_task函数中添加
if (strstr(data_str, "temperature") != nullptr) {
    // 处理温度数据
    float temp = extract_temperature(data_str);
    control_fan(temp);
} else if (strstr(data_str, "alarm") != nullptr) {
    // 处理报警数据
    trigger_alarm();
}
```

### 性能监控
两个设备都会定期输出统计信息：
- WiFi接收包数
- 蓝牙发送包数
- 连接状态
- 内存使用情况

## 下一步

1. **添加数据加密**：保护传输数据的安全性
2. **实现数据存储**：将接收的数据保存到SD卡或Flash
3. **添加Web界面**：通过浏览器监控系统状态
4. **集成传感器**：连接实际的传感器设备
5. **添加显示屏**：实时显示接收的数据

## 技术支持

如果遇到问题：
1. 检查ESP-IDF版本（推荐5.4.2+）
2. 查看详细的串口日志
3. 确认硬件连接正确
4. 参考完整的 `ESP32_BLUETOOTH_BRIDGE_GUIDE.md` 文档

祝你使用愉快！🎉
