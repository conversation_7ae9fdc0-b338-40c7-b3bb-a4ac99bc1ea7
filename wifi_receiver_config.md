# ESP32S3-PICO WiFi数据接收端配置指南

## 概述

本项目已成功集成WiFi数据接收功能，可以作为TCP服务器接收来自PlatformIO发送端的数据。

## 功能特性

- ✅ TCP服务器监听端口8080
- ✅ 支持多客户端连接（最多3个）
- ✅ JSON数据解析和显示
- ✅ 客户端连接状态监控
- ✅ 实时数据显示在屏幕上
- ✅ 详细的日志输出

## WiFi配置

### 方法1: 使用现有的WiFi配置系统

1. **首次启动时**：
   - 设备会自动进入WiFi配置模式
   - 创建名为"Xiaozhi-XXXXXX"的热点
   - 使用手机连接该热点
   - 在浏览器中访问 `http://192.168.4.1`
   - 选择您的WiFi网络并输入密码

2. **重新配置WiFi**：
   - 在设备启动时按住BOOT按钮
   - 或者触摸屏幕进入配置模式

### 方法2: 手动配置WiFi（开发调试用）

如果需要在代码中直接配置WiFi，可以修改 `components/wifi_data_receiver/example/wifi_receiver_example.c` 文件：

```c
// 修改这些参数为您的WiFi信息
#define WIFI_SSID "YOUR_WIFI_SSID"
#define WIFI_PASS "YOUR_WIFI_PASSWORD"
```

## 服务器配置

WiFi数据接收器的默认配置：

- **端口**: 8080
- **最大客户端数**: 3
- **接收超时**: 10秒
- **自动启动**: 是

## 获取接收端IP地址

### 方法1: 查看串口输出

连接ESP32S3到电脑，打开串口监视器（波特率115200），查找类似以下的输出：

```
I (12345) Application: 📡 WiFi Data Receiver started successfully!
I (12346) Application: 🌐 Server IP: *************
I (12347) Application: 🔌 Server Port: 8080
I (12348) Application: 👥 Max Clients: 3
I (12349) Application: 
I (12350) Application: 📋 Configuration for PlatformIO sender:
I (12351) Application:    wifi_serial_target_ip = "*************"
I (12352) Application:    wifi_serial_target_port = 8080
```

### 方法2: 查看设备屏幕

设备启动后，屏幕上会显示：
```
WiFi接收器已启动
IP: *************:8080
```

## PlatformIO发送端配置

将获取到的IP地址配置到PlatformIO发送端代码中：

```cpp
// 在PlatformIO项目的main.cpp中修改这些参数
const char* wifi_serial_target_ip = "*************";  // 替换为实际IP
const int wifi_serial_target_port = 8080;
```

## 支持的数据格式

接收端支持以下数据格式：

### JSON格式示例
```json
{
  "timestamp": 1234567890,
  "sensor_data": {
    "temperature": 25.6,
    "humidity": 60.2,
    "pressure": 1013.25
  },
  "message": "Hello from PlatformIO!",
  "device_status": "normal"
}
```

### 纯文本格式
任何纯文本数据都会被接收和显示。

## 数据接收处理

接收到的数据会：

1. **在串口输出详细日志**：
   - 客户端IP和端口
   - 数据长度
   - 原始数据内容
   - JSON解析结果（如果是JSON）

2. **在屏幕上显示通知**：
   - 客户端连接/断开状态
   - 接收到的消息内容
   - 传感器数据（如果有）

3. **解析JSON字段**：
   - `timestamp`: 时间戳
   - `sensor_data`: 传感器数据对象
   - `message`: 文本消息
   - `device_status`: 设备状态

## 故障排除

### 1. WiFi连接问题
- 确保ESP32S3和发送端在同一WiFi网络
- 检查WiFi密码是否正确
- 重启路由器和设备

### 2. 无法获取IP地址
- 检查串口输出是否有错误信息
- 确认WiFi连接成功
- 检查路由器DHCP设置

### 3. 发送端无法连接
- 确认IP地址和端口配置正确
- 检查防火墙设置
- 确认接收端服务器已启动

### 4. 数据接收异常
- 检查数据格式是否正确
- 查看串口日志了解详细错误
- 确认网络连接稳定

## 编译和烧录

1. **配置目标芯片**：
   ```bash
   idf.py set-target esp32s3
   ```

2. **配置项目**：
   ```bash
   idf.py menuconfig
   ```
   选择对应的板型：`Xiaozhi Assistant -> Board Type`

3. **编译项目**：
   ```bash
   idf.py build
   ```

4. **烧录到设备**：
   ```bash
   idf.py flash monitor
   ```

## 日志级别

如需调整日志详细程度，可以在 `idf.py menuconfig` 中设置：
```
Component config -> Log output -> Default log verbosity
```

建议开发时使用 `Info` 或 `Debug` 级别。

## 技术支持

如遇到问题，请：
1. 检查串口输出的完整日志
2. 确认网络配置正确
3. 验证数据格式符合要求
4. 查看本文档的故障排除部分
