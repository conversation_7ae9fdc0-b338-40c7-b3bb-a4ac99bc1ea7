#include "ble_server.h"
#include <cstring>

static const char* TAG = "BleServer";

BleServer* BleServer::instance_ = nullptr;

BleServer::BleServer() 
    : device_name_("ESP32-S3-BLE-Server")
    , advertising_(false)
    , gatts_if_(ESP_GATT_IF_NONE)
    , service_handle_(0)
    , char_handle_(0)
    , descr_handle_(0)
    , conn_id_(0) {
    
    // 初始化特征值
    memset(char_value_, 0, sizeof(char_value_));
    gatts_demo_char_val_.attr_max_len = GATTS_DEMO_CHAR_VAL_LEN_MAX;
    gatts_demo_char_val_.attr_len = 0;
    gatts_demo_char_val_.attr_value = char_value_;
    
    // 设置实例
    instance_ = this;
    BleManager::instance_ = this;
}

BleServer::~BleServer() {
    Deinitialize();
    instance_ = nullptr;
    BleManager::instance_ = nullptr;
}

BleServer* BleServer::GetInstance() {
    if (!instance_) {
        instance_ = new BleServer();
    }
    return instance_;
}

void BleServer::DestroyInstance() {
    if (instance_) {
        delete instance_;
        instance_ = nullptr;
    }
}

bool BleServer::Initialize() {
    if (IsInitializedInternal()) {
        ESP_LOGW(TAG, "BLE server already initialized");
        return true;
    }
    
    ESP_LOGI(TAG, "Initializing BLE server");
    
    // 初始化蓝牙控制器
    if (!InitializeBluetooth()) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth");
        return false;
    }
    
    // 初始化BLE
    if (!InitializeBle()) {
        ESP_LOGE(TAG, "Failed to initialize BLE");
        return false;
    }
    
    // 注册GATT应用
    esp_err_t ret = esp_ble_gatts_app_register(GATTS_APP_ID);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GATT app: %s", esp_err_to_name(ret));
        return false;
    }
    
    SetInitialized(true);
    ESP_LOGI(TAG, "BLE server initialized successfully");
    return true;
}

bool BleServer::Deinitialize() {
    if (!IsInitializedInternal()) {
        return true;
    }
    
    ESP_LOGI(TAG, "Deinitializing BLE server");
    
    StopAdvertising();
    
    if (gatts_if_ != ESP_GATT_IF_NONE) {
        esp_ble_gatts_app_unregister(gatts_if_);
    }
    
    esp_bluedroid_disable();
    esp_bluedroid_deinit();
    esp_bt_controller_disable();
    esp_bt_controller_deinit();
    
    SetInitialized(false);
    ESP_LOGI(TAG, "BLE server deinitialized");
    return true;
}

bool BleServer::StartAdvertising() {
    if (!IsInitializedInternal()) {
        ESP_LOGE(TAG, "BLE server not initialized");
        return false;
    }
    
    if (advertising_) {
        ESP_LOGW(TAG, "Already advertising");
        return true;
    }
    
    ESP_LOGI(TAG, "Starting BLE advertising");
    
    esp_err_t ret = esp_ble_gap_start_advertising(&adv_params_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start advertising: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool BleServer::StopAdvertising() {
    if (!advertising_) {
        return true;
    }
    
    ESP_LOGI(TAG, "Stopping BLE advertising");
    
    esp_err_t ret = esp_ble_gap_stop_advertising();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop advertising: %s", esp_err_to_name(ret));
        return false;
    }
    
    advertising_ = false;
    return true;
}

bool BleServer::SendData(const uint8_t* data, size_t length) {
    if (!IsClientConnected()) {
        ESP_LOGE(TAG, "No client connected");
        return false;
    }
    
    if (length > GATTS_DEMO_CHAR_VAL_LEN_MAX) {
        ESP_LOGE(TAG, "Data too long: %zu bytes (max %d)", length, GATTS_DEMO_CHAR_VAL_LEN_MAX);
        return false;
    }
    
    ESP_LOGI(TAG, "Sending data: %zu bytes", length);
    
    esp_err_t ret = esp_ble_gatts_send_indicate(gatts_if_, conn_id_, char_handle_, 
                                                length, const_cast<uint8_t*>(data), false);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to send indication: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

void BleServer::SetDeviceName(const std::string& name) {
    device_name_ = name;
    if (IsInitializedInternal()) {
        esp_ble_gap_set_device_name(device_name_.c_str());
    }
}

bool BleServer::SetupGattService() {
    ESP_LOGI(TAG, "Setting up GATT service");
    
    esp_gatt_srvc_id_t service_id;
    service_id.is_primary = true;
    service_id.id.inst_id = 0x00;
    service_id.id.uuid.len = ESP_UUID_LEN_16;
    service_id.id.uuid.uuid.uuid16 = GATTS_SERVICE_UUID_A;
    
    esp_err_t ret = esp_ble_gatts_create_service(gatts_if_, &service_id, GATTS_NUM_HANDLE_A);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create service: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

void BleServer::PrepareAdvertisingData() {
    // 清空广告数据
    memset(&adv_data_, 0, sizeof(adv_data_));
    
    adv_data_.set_scan_rsp = false;
    adv_data_.include_name = true;
    adv_data_.include_txpower = true;
    adv_data_.min_interval = 0x0006;
    adv_data_.max_interval = 0x0010;
    adv_data_.appearance = 0x00;
    adv_data_.manufacturer_len = 0;
    adv_data_.p_manufacturer_data = nullptr;
    adv_data_.service_data_len = 0;
    adv_data_.p_service_data = nullptr;
    adv_data_.service_uuid_len = 0;
    adv_data_.p_service_uuid = nullptr;
    adv_data_.flag = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT);
}

void BleServer::PrepareAdvertisingParams() {
    // 清空广告参数
    memset(&adv_params_, 0, sizeof(adv_params_));
    
    adv_params_.adv_int_min = 0x20;
    adv_params_.adv_int_max = 0x40;
    adv_params_.adv_type = ADV_TYPE_IND;
    adv_params_.own_addr_type = BLE_ADDR_TYPE_PUBLIC;
    adv_params_.channel_map = ADV_CHNL_ALL;
    adv_params_.adv_filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY;
}

void BleServer::HandleGapEvent(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) {
    switch (event) {
        case ESP_GAP_BLE_ADV_DATA_SET_COMPLETE_EVT:
            HandleGapAdvDataSetEvent(param);
            break;
        case ESP_GAP_BLE_ADV_START_COMPLETE_EVT:
            HandleGapAdvStartEvent(param);
            break;
        case ESP_GAP_BLE_ADV_STOP_COMPLETE_EVT:
            HandleGapAdvStopEvent(param);
            break;
        default:
            ESP_LOGD(TAG, "GAP event: %d", event);
            break;
    }
}

void BleServer::HandleGattsEvent(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param) {
    if (event == ESP_GATTS_REG_EVT) {
        if (param->reg.status == ESP_GATT_OK) {
            gatts_if_ = gatts_if;
        }
        HandleGattsRegisterEvent(param);
        return;
    }

    if (gatts_if == ESP_GATT_IF_NONE || gatts_if == gatts_if_) {
        switch (event) {
            case ESP_GATTS_CREATE_EVT:
                HandleGattsCreateEvent(param);
                break;
            case ESP_GATTS_ADD_CHAR_EVT:
                HandleGattsAddCharEvent(param);
                break;
            case ESP_GATTS_CONNECT_EVT:
                HandleGattsConnectEvent(param);
                break;
            case ESP_GATTS_DISCONNECT_EVT:
                HandleGattsDisconnectEvent(param);
                break;
            case ESP_GATTS_WRITE_EVT:
                HandleGattsWriteEvent(param);
                break;
            case ESP_GATTS_READ_EVT:
                HandleGattsReadEvent(param);
                break;
            default:
                ESP_LOGD(TAG, "GATTS event: %d", event);
                break;
        }
    }
}

void BleServer::HandleGattcEvent(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_ble_gattc_cb_param_t* param) {
    // BLE服务器通常不需要处理GATTC事件
    ESP_LOGD(TAG, "GATTC event: %d", event);
}

void BleServer::HandleGattsRegisterEvent(esp_ble_gatts_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTS register event, status: %d, app_id: %d", param->reg.status, param->reg.app_id);

    if (param->reg.status == ESP_GATT_OK) {
        // 设置设备名称
        esp_ble_gap_set_device_name(device_name_.c_str());

        // 准备广告数据
        PrepareAdvertisingData();
        esp_ble_gap_config_adv_data(&adv_data_);

        // 准备广告参数
        PrepareAdvertisingParams();

        // 创建GATT服务
        SetupGattService();
    }
}

void BleServer::HandleGattsCreateEvent(esp_ble_gatts_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTS create event, status: %d, service_handle: %d",
             param->create.status, param->create.service_handle);

    if (param->create.status == ESP_GATT_OK) {
        service_handle_ = param->create.service_handle;

        // 启动服务
        esp_ble_gatts_start_service(service_handle_);

        // 添加特征
        esp_bt_uuid_t char_uuid;
        char_uuid.len = ESP_UUID_LEN_16;
        char_uuid.uuid.uuid16 = GATTS_CHAR_UUID_A;

        esp_err_t ret = esp_ble_gatts_add_char(service_handle_, &char_uuid,
                                               ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                                               ESP_GATT_CHAR_PROP_BIT_READ | ESP_GATT_CHAR_PROP_BIT_WRITE | ESP_GATT_CHAR_PROP_BIT_NOTIFY,
                                               &gatts_demo_char_val_, nullptr);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to add characteristic: %s", esp_err_to_name(ret));
        }
    }
}

void BleServer::HandleGattsAddCharEvent(esp_ble_gatts_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTS add char event, status: %d, attr_handle: %d",
             param->add_char.status, param->add_char.attr_handle);

    if (param->add_char.status == ESP_GATT_OK) {
        char_handle_ = param->add_char.attr_handle;
    }
}

void BleServer::HandleGattsConnectEvent(esp_ble_gatts_cb_param_t* param) {
    ESP_LOGI(TAG, "Client connected, conn_id: %d", param->connect.conn_id);

    conn_id_ = param->connect.conn_id;
    memcpy(connected_device_, param->connect.remote_bda, sizeof(esp_bd_addr_t));
    connection_state_ = BLE_CONNECTED;

    // 停止广告
    StopAdvertising();

    // 通知连接状态变化
    if (connection_callback_) {
        connection_callback_(connection_state_, connected_device_);
    }
}

void BleServer::HandleGattsDisconnectEvent(esp_ble_gatts_cb_param_t* param) {
    ESP_LOGI(TAG, "Client disconnected, conn_id: %d", param->disconnect.conn_id);

    connection_state_ = BLE_DISCONNECTED;
    conn_id_ = 0;
    memset(connected_device_, 0, sizeof(esp_bd_addr_t));

    // 重新开始广告
    StartAdvertising();

    // 通知连接状态变化
    if (connection_callback_) {
        connection_callback_(connection_state_, connected_device_);
    }
}

void BleServer::HandleGattsWriteEvent(esp_ble_gatts_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTS write event, handle: %d, len: %d",
             param->write.handle, param->write.len);

    if (param->write.handle == char_handle_) {
        // 处理接收到的数据
        if (data_callback_) {
            data_callback_(param->write.value, param->write.len);
        }

        // 发送响应
        if (param->write.need_rsp) {
            esp_ble_gatts_send_response(gatts_if_, param->write.conn_id,
                                        param->write.trans_id, ESP_GATT_OK, nullptr);
        }
    }
}

void BleServer::HandleGattsReadEvent(esp_ble_gatts_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTS read event, handle: %d", param->read.handle);

    esp_gatt_rsp_t rsp;
    memset(&rsp, 0, sizeof(esp_gatt_rsp_t));
    rsp.attr_value.handle = param->read.handle;
    rsp.attr_value.len = gatts_demo_char_val_.attr_len;
    memcpy(rsp.attr_value.value, gatts_demo_char_val_.attr_value, rsp.attr_value.len);

    esp_ble_gatts_send_response(gatts_if_, param->read.conn_id,
                                param->read.trans_id, ESP_GATT_OK, &rsp);
}

void BleServer::HandleGapAdvDataSetEvent(esp_ble_gap_cb_param_t* param) {
    ESP_LOGI(TAG, "GAP adv data set complete, status: %d", param->adv_data_cmpl.status);

    if (param->adv_data_cmpl.status == ESP_BT_STATUS_SUCCESS) {
        // 广告数据设置成功，可以开始广告
        ESP_LOGI(TAG, "Advertisement data configured successfully");
    }
}

void BleServer::HandleGapAdvStartEvent(esp_ble_gap_cb_param_t* param) {
    ESP_LOGI(TAG, "GAP adv start complete, status: %d", param->adv_start_cmpl.status);

    if (param->adv_start_cmpl.status == ESP_BT_STATUS_SUCCESS) {
        advertising_ = true;
        ESP_LOGI(TAG, "Advertisement started successfully");
    } else {
        ESP_LOGE(TAG, "Failed to start advertisement");
    }
}

void BleServer::HandleGapAdvStopEvent(esp_ble_gap_cb_param_t* param) {
    ESP_LOGI(TAG, "GAP adv stop complete, status: %d", param->adv_stop_cmpl.status);

    advertising_ = false;
    ESP_LOGI(TAG, "Advertisement stopped");
}
