#ifndef WIFI_BT_BRIDGE_H
#define WIFI_BT_BRIDGE_H

#include "ble_server.h"
#include "wifi_data_receiver.h"
#include <string>
#include <functional>

/**
 * @brief WiFi到蓝牙桥接器类
 * 
 * 这个类负责：
 * 1. 接收WiFi数据
 * 2. 通过蓝牙转发给连接的设备
 * 3. 管理连接状态
 */
class WiFiBtBridge {
public:
    // 状态回调函数类型
    typedef std::function<void(const std::string& message)> status_callback_t;
    typedef std::function<void(const std::string& data, const std::string& source)> data_forward_callback_t;

private:
    BleServer* ble_server_;
    bool wifi_receiver_initialized_;
    bool ble_server_initialized_;
    bool bridge_running_;
    
    // 配置参数
    std::string bt_device_name_;
    uint16_t wifi_port_;
    int max_wifi_clients_;
    
    // 回调函数
    status_callback_t status_callback_;
    data_forward_callback_t data_forward_callback_;
    
    // 统计信息
    uint32_t wifi_packets_received_;
    uint32_t ble_packets_sent_;
    uint32_t ble_packets_failed_;

public:
    /**
     * @brief 构造函数
     * 
     * @param bt_device_name BLE设备名称
     * @param wifi_port WiFi接收端口
     * @param max_wifi_clients 最大WiFi客户端数量
     */
    WiFiBtBridge(const std::string& bt_device_name = "ESP32-S3-PICO-Bridge",
                 uint16_t wifi_port = 8080,
                 int max_wifi_clients = 5);
    
    /**
     * @brief 析构函数
     */
    ~WiFiBtBridge();
    
    /**
     * @brief 初始化桥接器
     * 
     * @return true 成功
     * @return false 失败
     */
    bool Initialize();
    
    /**
     * @brief 启动桥接器
     * 
     * @return true 成功
     * @return false 失败
     */
    bool Start();
    
    /**
     * @brief 停止桥接器
     * 
     * @return true 成功
     * @return false 失败
     */
    bool Stop();
    
    /**
     * @brief 反初始化桥接器
     */
    void Deinitialize();
    
    /**
     * @brief 设置状态回调函数
     * 
     * @param callback 回调函数
     */
    void SetStatusCallback(status_callback_t callback);
    
    /**
     * @brief 设置数据转发回调函数
     * 
     * @param callback 回调函数
     */
    void SetDataForwardCallback(data_forward_callback_t callback);
    
    /**
     * @brief 获取运行状态
     * 
     * @return true 正在运行
     * @return false 已停止
     */
    bool IsRunning() const { return bridge_running_; }
    
    /**
     * @brief 获取蓝牙连接状态
     * 
     * @return true 已连接
     * @return false 未连接
     */
    bool IsBtClientConnected() const;
    
    /**
     * @brief 获取WiFi客户端数量
     * 
     * @return int 客户端数量
     */
    int GetWifiClientCount() const;
    
    /**
     * @brief 获取统计信息
     * 
     * @param wifi_received WiFi接收包数
     * @param bt_sent 蓝牙发送包数
     * @param bt_failed 蓝牙发送失败包数
     */
    void GetStatistics(uint32_t& wifi_received, uint32_t& bt_sent, uint32_t& bt_failed) const;
    
    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

private:
    /**
     * @brief 初始化WiFi接收器
     * 
     * @return true 成功
     * @return false 失败
     */
    bool InitializeWifiReceiver();
    
    /**
     * @brief 初始化BLE服务器
     *
     * @return true 成功
     * @return false 失败
     */
    bool InitializeBleServer();
    
    /**
     * @brief WiFi数据接收回调
     * 
     * @param packet 数据包
     * @param user_data 用户数据
     */
    static void WifiDataReceivedCallback(const wifi_data_packet_t* packet, void* user_data);
    
    /**
     * @brief WiFi客户端状态回调
     * 
     * @param client_ip 客户端IP
     * @param client_port 客户端端口
     * @param connected 连接状态
     * @param user_data 用户数据
     */
    static void WifiClientStatusCallback(const char* client_ip, uint16_t client_port, 
                                       bool connected, void* user_data);
    
    /**
     * @brief BLE连接状态回调
     *
     * @param state 连接状态
     * @param address 设备地址
     */
    void BleConnectionCallback(BleConnectionState state, const esp_bd_addr_t& address);
    
    /**
     * @brief 处理WiFi数据
     * 
     * @param packet 数据包
     */
    void HandleWifiData(const wifi_data_packet_t* packet);
    
    /**
     * @brief 处理WiFi客户端状态变化
     * 
     * @param client_ip 客户端IP
     * @param client_port 客户端端口
     * @param connected 连接状态
     */
    void HandleWifiClientStatus(const char* client_ip, uint16_t client_port, bool connected);
    
    /**
     * @brief 通过BLE转发数据
     *
     * @param data 数据
     * @param length 数据长度
     * @param source 数据源描述
     * @return true 成功
     * @return false 失败
     */
    bool ForwardDataViaBle(const uint8_t* data, size_t length, const std::string& source);
    
    /**
     * @brief 发送状态消息
     * 
     * @param message 消息内容
     */
    void SendStatusMessage(const std::string& message);
};

#endif // WIFI_BT_BRIDGE_H
