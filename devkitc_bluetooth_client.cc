#include "devkitc_bluetooth_client.h"
#include "esp_log.h"
#include "esp_bt.h"
#include "esp_bt_main.h"
#include <cstring>

static const char* TAG = "DevKitC_BT_Client";

DevKitCBluetoothClient* DevKitCBluetoothClient::instance_ = nullptr;

DevKitCBluetoothClient::DevKitCBluetoothClient(const std::string& target_device_name)
    : connection_state_(DISCONNECTED)
    , initialized_(false)
    , discovering_(false)
    , spp_handle_(0)
    , target_device_name_(target_device_name)
    , target_found_(false)
    , discovery_timeout_sec_(30)
    , connection_timeout_sec_(10)
    , auto_reconnect_(true)
    , packets_received_(0)
    , bytes_received_(0)
    , connection_attempts_(0) {
    
    instance_ = this;
    memset(target_address_, 0, sizeof(target_address_));
    
    ESP_LOGI(TAG, "DevKitC Bluetooth Client created");
    ESP_LOGI(TAG, "Target device: %s", target_device_name_.c_str());
}

DevKitCBluetoothClient::~DevKitCBluetoothClient() {
    Deinitialize();
    instance_ = nullptr;
    ESP_LOGI(TAG, "DevKitC Bluetooth Client destroyed");
}

bool DevKitCBluetoothClient::Initialize() {
    ESP_LOGI(TAG, "Initializing Bluetooth Client");
    
    if (initialized_) {
        ESP_LOGW(TAG, "Already initialized");
        return true;
    }
    
    // 初始化蓝牙控制器
    if (!InitializeController()) {
        ESP_LOGE(TAG, "Failed to initialize controller");
        return false;
    }
    
    // 初始化GAP
    if (!InitializeGap()) {
        ESP_LOGE(TAG, "Failed to initialize GAP");
        return false;
    }
    
    // 初始化SPP
    if (!InitializeSpp()) {
        ESP_LOGE(TAG, "Failed to initialize SPP");
        return false;
    }
    
    initialized_ = true;
    ESP_LOGI(TAG, "Bluetooth Client initialized successfully");
    return true;
}

void DevKitCBluetoothClient::Deinitialize() {
    if (!initialized_) {
        return;
    }
    
    ESP_LOGI(TAG, "Deinitializing Bluetooth Client");
    
    // 断开连接
    Disconnect();
    
    // 停止发现
    StopDiscovery();
    
    // 反初始化蓝牙栈
    esp_spp_deinit();
    esp_bluedroid_disable();
    esp_bluedroid_deinit();
    esp_bt_controller_disable();
    esp_bt_controller_deinit();
    
    initialized_ = false;
    ESP_LOGI(TAG, "Bluetooth Client deinitialized");
}

bool DevKitCBluetoothClient::StartDiscovery(int timeout_sec) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Not initialized");
        return false;
    }
    
    if (discovering_) {
        ESP_LOGW(TAG, "Already discovering");
        return true;
    }
    
    ESP_LOGI(TAG, "Starting device discovery (timeout: %d sec)", timeout_sec);
    
    // 清空之前发现的设备
    discovered_devices_.clear();
    target_found_ = false;
    
    // 设置发现参数
    esp_bt_gap_set_scan_mode(ESP_BT_CONNECTABLE, ESP_BT_GENERAL_DISCOVERABLE);
    
    // 开始发现
    esp_err_t ret = esp_bt_gap_start_discovery(ESP_BT_INQ_MODE_GENERAL_INQUIRY, timeout_sec, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start discovery: %s", esp_err_to_name(ret));
        return false;
    }
    
    discovering_ = true;
    connection_state_ = DISCOVERING;
    discovery_timeout_sec_ = timeout_sec;
    
    SendStatusMessage("Started device discovery");
    return true;
}

bool DevKitCBluetoothClient::StopDiscovery() {
    if (!discovering_) {
        return true;
    }
    
    ESP_LOGI(TAG, "Stopping device discovery");
    
    esp_err_t ret = esp_bt_gap_cancel_discovery();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop discovery: %s", esp_err_to_name(ret));
        return false;
    }
    
    discovering_ = false;
    if (connection_state_ == DISCOVERING) {
        connection_state_ = DISCONNECTED;
    }
    
    SendStatusMessage("Stopped device discovery");
    return true;
}

bool DevKitCBluetoothClient::ConnectToTarget() {
    if (!target_found_) {
        ESP_LOGW(TAG, "Target device not found, starting discovery first");
        return StartDiscovery();
    }
    
    return ConnectToDevice(target_address_);
}

bool DevKitCBluetoothClient::ConnectToDevice(const esp_bd_addr_t& address) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Not initialized");
        return false;
    }
    
    if (connection_state_ == CONNECTED) {
        ESP_LOGW(TAG, "Already connected");
        return true;
    }
    
    if (connection_state_ == CONNECTING) {
        ESP_LOGW(TAG, "Already connecting");
        return true;
    }
    
    ESP_LOGI(TAG, "Connecting to device: %02x:%02x:%02x:%02x:%02x:%02x",
             address[0], address[1], address[2], address[3], address[4], address[5]);
    
    // 停止发现（如果正在进行）
    if (discovering_) {
        StopDiscovery();
    }
    
    // 开始连接
    esp_err_t ret = esp_spp_connect(ESP_SPP_SEC_AUTHENTICATE, ESP_SPP_ROLE_MASTER, 0, address);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start connection: %s", esp_err_to_name(ret));
        return false;
    }
    
    connection_state_ = CONNECTING;
    connection_attempts_++;
    memcpy(target_address_, address, sizeof(esp_bd_addr_t));
    
    SendStatusMessage("Connecting to device...");
    return true;
}

bool DevKitCBluetoothClient::Disconnect() {
    if (connection_state_ != CONNECTED) {
        ESP_LOGW(TAG, "Not connected");
        return true;
    }
    
    ESP_LOGI(TAG, "Disconnecting from device");
    
    esp_err_t ret = esp_spp_disconnect(spp_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to disconnect: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool DevKitCBluetoothClient::SendData(const uint8_t* data, size_t length) {
    if (connection_state_ != CONNECTED) {
        ESP_LOGW(TAG, "Not connected, cannot send data");
        return false;
    }
    
    esp_err_t ret = esp_spp_write(spp_handle_, length, (uint8_t*)data);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to send data: %s", esp_err_to_name(ret));
        return false;
    }
    
    ESP_LOGD(TAG, "Sent %d bytes", length);
    return true;
}

bool DevKitCBluetoothClient::SendString(const std::string& message) {
    return SendData((const uint8_t*)message.c_str(), message.length());
}

void DevKitCBluetoothClient::SetDataReceivedCallback(data_received_callback_t callback) {
    data_callback_ = callback;
}

void DevKitCBluetoothClient::SetConnectionStatusCallback(connection_status_callback_t callback) {
    connection_callback_ = callback;
}

void DevKitCBluetoothClient::SetStatusCallback(status_callback_t callback) {
    status_callback_ = callback;
}

void DevKitCBluetoothClient::SetTargetDeviceName(const std::string& name) {
    target_device_name_ = name;
    target_found_ = false;
    ESP_LOGI(TAG, "Target device name set to: %s", name.c_str());
}

void DevKitCBluetoothClient::SetAutoReconnect(bool enable) {
    auto_reconnect_ = enable;
    ESP_LOGI(TAG, "Auto reconnect %s", enable ? "enabled" : "disabled");
}

const std::vector<DevKitCBluetoothClient::DiscoveredDevice>& DevKitCBluetoothClient::GetDiscoveredDevices() const {
    return discovered_devices_;
}

void DevKitCBluetoothClient::GetStatistics(uint32_t& packets_received, uint32_t& bytes_received, 
                                         uint32_t& connection_attempts) const {
    packets_received = packets_received_;
    bytes_received = bytes_received_;
    connection_attempts = connection_attempts_;
}

void DevKitCBluetoothClient::ResetStatistics() {
    packets_received_ = 0;
    bytes_received_ = 0;
    connection_attempts_ = 0;
    ESP_LOGI(TAG, "Statistics reset");
}

bool DevKitCBluetoothClient::InitializeController() {
    ESP_LOGI(TAG, "Initializing BT controller");
    
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    esp_err_t ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bt_controller_enable(ESP_BT_MODE_CLASSIC_BT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool DevKitCBluetoothClient::InitializeGap() {
    ESP_LOGI(TAG, "Initializing GAP");
    
    esp_err_t ret = esp_bt_gap_register_callback(StaticGapCallback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GAP callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool DevKitCBluetoothClient::InitializeSpp() {
    ESP_LOGI(TAG, "Initializing SPP");
    
    esp_err_t ret = esp_spp_register_callback(StaticSppCallback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register SPP callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_spp_init(ESP_SPP_MODE_CB);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize SPP: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

void DevKitCBluetoothClient::StaticGapCallback(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGapEvent(event, param);
    }
}

void DevKitCBluetoothClient::StaticSppCallback(esp_spp_cb_event_t event, esp_spp_cb_param_t* param) {
    if (instance_) {
        instance_->HandleSppEvent(event, param);
    }
}

void DevKitCBluetoothClient::HandleGapEvent(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param) {
    switch (event) {
        case ESP_BT_GAP_DISC_RES_EVT:
            HandleDeviceDiscovered(param);
            break;

        case ESP_BT_GAP_DISC_STATE_CHANGED_EVT:
            if (param->disc_st_chg.state == ESP_BT_GAP_DISCOVERY_STOPPED) {
                ESP_LOGI(TAG, "Device discovery completed");
                discovering_ = false;
                if (connection_state_ == DISCOVERING) {
                    connection_state_ = DISCONNECTED;
                }
                SendStatusMessage("Device discovery completed");

                // 如果找到目标设备，自动连接
                if (target_found_ && auto_reconnect_) {
                    ESP_LOGI(TAG, "Target device found, attempting to connect");
                    ConnectToDevice(target_address_);
                }
            }
            break;

        case ESP_BT_GAP_AUTH_CMPL_EVT:
            if (param->auth_cmpl.stat == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "Authentication successful");
            } else {
                ESP_LOGE(TAG, "Authentication failed: %d", param->auth_cmpl.stat);
            }
            break;

        default:
            ESP_LOGD(TAG, "GAP event: %d", event);
            break;
    }
}

void DevKitCBluetoothClient::HandleSppEvent(esp_spp_cb_event_t event, esp_spp_cb_param_t* param) {
    switch (event) {
        case ESP_SPP_INIT_EVT:
            ESP_LOGI(TAG, "SPP initialized");
            break;

        case ESP_SPP_OPEN_EVT:
            if (param->open.status == ESP_SPP_SUCCESS) {
                ESP_LOGI(TAG, "SPP connection opened successfully");
                HandleConnectionEstablished(param->open.handle);
            } else {
                ESP_LOGE(TAG, "SPP connection failed: %d", param->open.status);
                connection_state_ = DISCONNECTED;
                SendStatusMessage("Connection failed");

                // 自动重连
                if (auto_reconnect_) {
                    ESP_LOGI(TAG, "Attempting to reconnect in 5 seconds...");
                    // 这里可以添加延时重连逻辑
                }
            }
            break;

        case ESP_SPP_CLOSE_EVT:
            ESP_LOGI(TAG, "SPP connection closed");
            HandleConnectionClosed();
            break;

        case ESP_SPP_DATA_IND_EVT:
            ESP_LOGD(TAG, "SPP data received: %d bytes", param->data_ind.len);
            HandleDataReceived(param->data_ind.data, param->data_ind.len);
            break;

        default:
            ESP_LOGD(TAG, "SPP event: %d", event);
            break;
    }
}

void DevKitCBluetoothClient::HandleDeviceDiscovered(esp_bt_gap_cb_param_t* param) {
    esp_bt_gap_dev_prop_t* prop = param->disc_res.prop;

    DiscoveredDevice device;
    memcpy(device.address, param->disc_res.bda, sizeof(esp_bd_addr_t));
    device.rssi = param->disc_res.rssi;
    device.cod = param->disc_res.cod;
    device.name = "";

    // 解析设备属性
    for (int i = 0; i < param->disc_res.num_prop; i++) {
        if (prop[i].type == ESP_BT_GAP_DEV_PROP_BDNAME) {
            device.name = std::string((char*)prop[i].val, prop[i].len);
            break;
        }
    }

    ESP_LOGI(TAG, "Device discovered: %s [%02x:%02x:%02x:%02x:%02x:%02x] RSSI: %d",
             device.name.c_str(),
             device.address[0], device.address[1], device.address[2],
             device.address[3], device.address[4], device.address[5],
             device.rssi);

    // 添加到发现列表
    discovered_devices_.push_back(device);

    // 检查是否是目标设备
    if (device.name == target_device_name_) {
        ESP_LOGI(TAG, "Target device found: %s", target_device_name_.c_str());
        memcpy(target_address_, device.address, sizeof(esp_bd_addr_t));
        target_found_ = true;
        SendStatusMessage("Target device found: " + device.name);
    }
}

void DevKitCBluetoothClient::HandleConnectionEstablished(uint32_t handle) {
    spp_handle_ = handle;
    connection_state_ = CONNECTED;

    ESP_LOGI(TAG, "Connection established successfully");
    SendStatusMessage("Connected to " + target_device_name_);

    if (connection_callback_) {
        connection_callback_(true, target_device_name_);
    }
}

void DevKitCBluetoothClient::HandleConnectionClosed() {
    connection_state_ = DISCONNECTED;
    spp_handle_ = 0;

    ESP_LOGI(TAG, "Connection closed");
    SendStatusMessage("Disconnected from " + target_device_name_);

    if (connection_callback_) {
        connection_callback_(false, target_device_name_);
    }

    // 自动重连
    if (auto_reconnect_ && target_found_) {
        ESP_LOGI(TAG, "Attempting to reconnect...");
        ConnectToDevice(target_address_);
    }
}

void DevKitCBluetoothClient::HandleDataReceived(const uint8_t* data, size_t length) {
    packets_received_++;
    bytes_received_ += length;

    ESP_LOGD(TAG, "Data received: %d bytes", length);

    if (data_callback_) {
        data_callback_(data, length);
    }
}

void DevKitCBluetoothClient::SendStatusMessage(const std::string& message) {
    ESP_LOGI(TAG, "Status: %s", message.c_str());

    if (status_callback_) {
        status_callback_(message);
    }
}
