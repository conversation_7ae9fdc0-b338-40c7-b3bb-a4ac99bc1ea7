# 小智语音警报修复指南

## 问题描述

当前小智在超过4次警报时，语音模块不会发出正常语音，只会播放震动声音。需要修复为：
1. 超过4次时自动唤醒小智
2. 播放正常的语音提醒内容
3. 而不是仅仅播放震动声音

## 当前问题分析

### 1. 警报阈值问题
当前配置：
- `MAX_HISTORY_SIZE = 5` (记录最近5次数据)
- `ALERT_THRESHOLD = 3` (3次就触发警报)
- 需要修改为4次才触发

### 2. 语音播放问题
当前在 `TriggerSmartAlert` 函数中：
```cpp
// 当前代码只播放简单音效
PlaySoundImmediate(Lang::Sounds::P3_SUCCESS);
```

需要修改为：
1. 自动唤醒小智
2. 播放真正的语音提醒
3. 而不是震动声音

## 解决方案

### 第一步：修改警报阈值配置

修改 `main/application.h` 文件中的 `DataHistory` 结构体：

```cpp
struct DataHistory {
    std::deque<std::string> emotion_history;
    std::deque<std::string> posture_history;
    std::string last_emotion_alert;
    std::string last_posture_alert;
    uint32_t last_alert_time = 0;
    static const size_t MAX_HISTORY_SIZE = 5;
    static const size_t ALERT_THRESHOLD = 4;  // 修改：从3改为4
    static const uint32_t ALERT_COOLDOWN_MS = 30000; // 修改：从5000改为30000（30秒）
};
```

### 第二步：修复语音播放逻辑

修改 `main/application.cc` 文件中的 `TriggerSmartAlert` 函数：

```cpp
void Application::TriggerSmartAlert(const std::string& type, const std::string& state) {
    uint32_t current_time = esp_timer_get_time() / 1000;

    // 检查设备状态 - 如果设备忙碌，强制唤醒
    if (device_state_ != kDeviceStateIdle) {
        ESP_LOGI(TAG, "🚨 Device busy, forcing wake up for critical alert");
        // 强制停止当前活动并回到空闲状态
        if (device_state_ == kDeviceStateSpeaking) {
            AbortSpeaking(kAbortReasonNone);
        } else if (device_state_ == kDeviceStateListening) {
            StopListening();
        }
        SetDeviceState(kDeviceStateIdle);
    }

    // 检查冷却时间
    uint32_t time_since_last_alert = current_time - data_history_.last_alert_time;
    if (time_since_last_alert < DataHistory::ALERT_COOLDOWN_MS) {
        uint32_t remaining_cooldown = DataHistory::ALERT_COOLDOWN_MS - time_since_last_alert;
        ESP_LOGI(TAG, "⏰ Alert cooldown active, skipping alert. Remaining: %u ms", remaining_cooldown);
        return;
    }

    // 更新最后警报时间
    data_history_.last_alert_time = current_time;

    // 构建警报消息
    std::string message;
    std::string emotion = "concerned";

    if (type == "emotion") {
        if (state == "Sad") {
            message = "我注意到你最近看起来有些难过，要不要聊聊天？";
            emotion = "sad";
        } else if (state == "Angry") {
            message = "你看起来有点生气，深呼吸一下，放松心情吧。";
            emotion = "angry";
        } else if (state == "Happy") {
            message = "你看起来心情很好呢，真棒！";
            emotion = "happy";
        }
        data_history_.last_emotion_alert = state;
    } else if (type == "posture") {
        if (state == "Poor") {
            message = "你已经保持不良坐姿一段时间了，记得调整一下坐姿哦！";
            emotion = "concerned";
        } else if (state == "Good") {
            message = "你的坐姿保持得很好，继续保持！";
            emotion = "happy";
        }
        data_history_.last_posture_alert = state;
    }

    if (!message.empty()) {
        ESP_LOGI(TAG, "🚨 Critical Smart Alert (>4 times): %s", message.c_str());

        Schedule([this, message, emotion]() {
            // 1. 强制唤醒设备
            ESP_LOGI(TAG, "🎯 Force waking up device for critical alert");
            
            // 2. 设置显示信息
            auto display = Board::GetInstance().GetDisplay();
            display->SetChatMessage("assistant", message.c_str());
            display->SetStatus("紧急提醒");
            display->SetEmotion(emotion.c_str());

            // 3. 播放唤醒音效
            ESP_LOGI(TAG, "🔊 Playing wake-up sound");
            ResetDecoder();
            PlaySound(Lang::Sounds::P3_POPUP);
            
            // 4. 等待音效播放完成，然后发送TTS请求
            vTaskDelay(pdMS_TO_TICKS(500));
            
            // 5. 发送TTS请求进行语音播报
            ESP_LOGI(TAG, "🗣️ Sending TTS request for voice alert");
            if (protocol_) {
                // 构建TTS请求
                cJSON* tts_request = cJSON_CreateObject();
                cJSON_AddStringToObject(tts_request, "type", "tts");
                cJSON_AddStringToObject(tts_request, "text", message.c_str());
                cJSON_AddStringToObject(tts_request, "voice", "xiaozhi");
                cJSON_AddNumberToObject(tts_request, "speed", 1.0);
                
                char* json_string = cJSON_Print(tts_request);
                if (json_string) {
                    protocol_->SendMessage(json_string);
                    free(json_string);
                }
                cJSON_Delete(tts_request);
            }
            
            ESP_LOGI(TAG, "✅ Critical alert processed with voice synthesis");
        });
    }
}
```

### 第三步：添加强制唤醒功能

在 `main/application.h` 中添加新方法：

```cpp
public:
    // 现有方法...
    void ForceWakeUpForAlert();  // 新增：强制唤醒用于警报

private:
    // 现有成员...
    bool is_critical_alert_active_ = false;  // 新增：标记是否有紧急警报
```

在 `main/application.cc` 中实现：

```cpp
void Application::ForceWakeUpForAlert() {
    ESP_LOGI(TAG, "🚨 Force wake up for critical alert");
    
    // 停止当前所有活动
    if (device_state_ == kDeviceStateSpeaking) {
        AbortSpeaking(kAbortReasonNone);
    } else if (device_state_ == kDeviceStateListening) {
        StopListening();
    }
    
    // 确保音频输出启用
    auto codec = Board::GetInstance().GetAudioCodec();
    if (codec && !codec->output_enabled()) {
        codec->EnableOutput(true);
    }
    
    // 设置为空闲状态，准备接收语音
    SetDeviceState(kDeviceStateIdle);
    is_critical_alert_active_ = true;
    
    ESP_LOGI(TAG, "✅ Device ready for critical alert voice playback");
}
```

### 第四步：修改音频播放优先级

修改 `PlaySoundImmediate` 函数，为紧急警报提供更高优先级：

```cpp
void Application::PlaySoundImmediate(const std::string_view& sound) {
    ESP_LOGI(TAG, "🎵 Playing immediate sound (%d bytes)", (int)sound.size());

    auto codec = Board::GetInstance().GetAudioCodec();
    if (!codec) {
        ESP_LOGE(TAG, "❌ Audio codec not available");
        return;
    }

    if (!codec->output_enabled()) {
        ESP_LOGW(TAG, "⚠️ Audio output not enabled, enabling now");
        codec->EnableOutput(true);
    }

    // 如果是紧急警报，清空现有音频队列
    if (is_critical_alert_active_) {
        std::lock_guard<std::mutex> lock(mutex_);
        audio_decode_queue_.clear();
        ESP_LOGI(TAG, "🚨 Cleared audio queue for critical alert");
    }

    // 处理音频数据...
    const char* data = sound.data();
    size_t size = sound.size();

    if (size == 0 || data == nullptr) {
        ESP_LOGE(TAG, "❌ Sound data is empty or null");
        return;
    }

    for (const char* p = data; p < data + size; ) {
        auto p3 = (BinaryProtocol3*)p;
        p += sizeof(BinaryProtocol3);

        auto payload_size = ntohs(p3->payload_size);
        AudioStreamPacket packet;
        packet.sample_rate = 16000;
        packet.frame_duration = 60;
        packet.payload.resize(payload_size);
        memcpy(packet.payload.data(), p3->payload, payload_size);
        p += payload_size;

        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (is_critical_alert_active_) {
                // 紧急警报音频放在队列最前面
                audio_decode_queue_.emplace_front(std::move(packet));
            } else {
                audio_decode_queue_.emplace_back(std::move(packet));
            }
        }
    }
    
    // 重置紧急警报标志
    if (is_critical_alert_active_) {
        is_critical_alert_active_ = false;
    }
    
    ESP_LOGI(TAG, "✅ Immediate sound queued for playback");
}
```

### 第五步：测试验证

创建测试脚本验证修复效果：

```python
#!/usr/bin/env python3
"""
测试超过4次警报的语音播放修复
"""

import socket
import json
import time

XIAOZHI_IP = "*************"  # 修改为你的小智IP
XIAOZHI_PORT = 8080

def send_data(emotion, posture, confidence=0.85):
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect((XIAOZHI_IP, XIAOZHI_PORT))
        
        data = {
            "emotion": emotion,
            "posture": posture,
            "confidence": confidence,
            "timestamp": int(time.time() * 1000)
        }
        
        json_data = json.dumps(data)
        sock.send(json_data.encode('utf-8'))
        print(f"✅ 发送: {emotion}, {posture}")
        sock.close()
        return True
    except Exception as e:
        print(f"❌ 发送失败: {e}")
        return False

def test_voice_alert_fix():
    """测试语音警报修复"""
    print("🎯 测试超过4次警报的语音播放修复")
    print("=" * 50)
    
    # 发送5次相同的负面情绪，第5次应该触发语音警报
    emotions = ["Sad", "Sad", "Sad", "Sad", "Sad"]
    
    for i, emotion in enumerate(emotions, 1):
        print(f"📤 发送第{i}次数据: {emotion}")
        send_data(emotion, "Good Posture")
        
        if i == 5:
            print("🚨 预期：应该触发语音警报（而不是震动声音）")
            print("👂 请听小智是否播放了语音提醒...")
        
        time.sleep(3)  # 等待3秒
    
    print("\n✅ 测试完成！")
    print("如果修复成功，小智应该：")
    print("1. 自动唤醒")
    print("2. 播放语音提醒内容")
    print("3. 而不是仅仅播放震动声音")

if __name__ == "__main__":
    test_voice_alert_fix()
```

## 修改总结

### 关键修改点：

1. **警报阈值**：从3次改为4次触发
2. **强制唤醒**：超过4次时强制唤醒设备
3. **语音播放**：发送TTS请求而不是仅播放音效
4. **音频优先级**：紧急警报音频优先播放
5. **冷却时间**：从5秒改为30秒

### 预期效果：

- 超过4次相同状态时触发警报
- 自动唤醒小智并播放语音提醒
- 不再只播放震动声音
- 提供更好的用户体验

### 测试方法：

1. 连续发送5次相同的负面情绪数据
2. 观察第5次时是否触发语音警报
3. 确认播放的是语音内容而不是震动声音

按照这个指南修改代码后，小智应该能够在超过4次警报时正常播放语音提醒，而不是仅仅播放震动声音。
