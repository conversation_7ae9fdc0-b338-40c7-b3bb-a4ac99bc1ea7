# VS Code ESP-IDF Build Script
# This script uses the same paths as VS Code ESP-IDF extension

Write-Host "========================================" -ForegroundColor Green
Write-Host "VS Code ESP-IDF Build Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "CMakeLists.txt")) {
    Write-Host "Error: CMakeLists.txt not found. Please run this script from the project root directory." -ForegroundColor Red
    exit 1
}

# Use the same paths as VS Code ESP-IDF extension
$IDF_PATH = "C:\Users\<USER>\esp\v5.4.2\esp-idf\v5.4.2\esp-idf"
$PYTHON_PATH = "d:\esp32-idf-ahy\5.3.2\tools\tools\idf-python\3.11.2\python.exe"
$TOOLS_PATH = "d:\esp32-idf-ahy\5.3.2\tools"

Write-Host "Using ESP-IDF at: $IDF_PATH" -ForegroundColor Cyan
Write-Host "Using Python at: $PYTHON_PATH" -ForegroundColor Cyan
Write-Host "Using Tools at: $TOOLS_PATH" -ForegroundColor Cyan

# Check if paths exist
if (-not (Test-Path $IDF_PATH)) {
    Write-Host "Error: ESP-IDF not found at $IDF_PATH" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $PYTHON_PATH)) {
    Write-Host "Error: Python not found at $PYTHON_PATH" -ForegroundColor Red
    exit 1
}

# Set environment variables like VS Code does
$env:IDF_PATH = $IDF_PATH
$env:IDF_TOOLS_PATH = $TOOLS_PATH
$env:PATH = "$TOOLS_PATH\tools\ninja\1.12.1;$TOOLS_PATH\tools\cmake\3.30.2\bin;$TOOLS_PATH\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin;" + $env:PATH

Write-Host "Environment setup completed" -ForegroundColor Green

# Clean build directory if it exists
if (Test-Path "build") {
    Write-Host "Cleaning existing build directory..." -ForegroundColor Yellow
    try {
        Remove-Item "build" -Recurse -Force
        Write-Host "✓ Build directory cleaned" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Could not clean build directory: $_" -ForegroundColor Yellow
    }
}

# Start the build
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Starting Build" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

try {
    $idfPy = "$IDF_PATH\tools\idf.py"
    
    Write-Host "Executing: $PYTHON_PATH $idfPy build" -ForegroundColor Cyan
    
    # Run the build command with verbose output
    & $PYTHON_PATH $idfPy build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n========================================" -ForegroundColor Green
        Write-Host "Build completed successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        
        # Show build artifacts
        if (Test-Path "build\xiaozhi.bin") {
            $binSize = (Get-Item "build\xiaozhi.bin").Length
            Write-Host "Firmware binary: build\xiaozhi.bin ($([math]::Round($binSize/1KB, 2)) KB)" -ForegroundColor Cyan
        }
        
        if (Test-Path "build") {
            Write-Host "Build directory contents:" -ForegroundColor Yellow
            Get-ChildItem "build" -Name | Select-Object -First 10 | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        }
        
    } else {
        Write-Host "`nBuild failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "`nBuild failed with exception: $_" -ForegroundColor Red
    Write-Host "Exception details: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
