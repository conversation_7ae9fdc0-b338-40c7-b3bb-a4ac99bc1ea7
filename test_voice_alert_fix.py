#!/usr/bin/env python3
"""
测试小智语音警报修复效果
验证超过4次警报时是否播放真正的语音而不是震动声音
"""

import socket
import json
import time
import argparse

def send_data(ip, port, emotion, posture, confidence=0.85):
    """发送单条数据到小智"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect((ip, port))
        
        data = {
            "emotion": emotion,
            "posture": posture,
            "confidence": confidence,
            "timestamp": int(time.time() * 1000)
        }
        
        json_data = json.dumps(data)
        sock.send(json_data.encode('utf-8'))
        print(f"✅ 发送: {emotion}, {posture}")
        sock.close()
        return True
    except Exception as e:
        print(f"❌ 发送失败: {e}")
        return False

def test_voice_alert_fix(ip, port):
    """测试语音警报修复"""
    print("🎯 测试小智语音警报修复效果")
    print("=" * 60)
    print(f"目标设备: {ip}:{port}")
    print("修复内容:")
    print("1. 警报阈值从3次改为4次")
    print("2. 超过4次时自动唤醒小智")
    print("3. 播放真正的语音提醒而不是震动声音")
    print("4. 冷却时间从5秒改为30秒")
    print()
    
    # 测试连接
    print("🔗 测试连接...")
    if not send_data(ip, port, "Happy", "Good Posture"):
        print("❌ 无法连接到小智设备，请检查IP地址和端口")
        return False
    
    print("✅ 连接成功！")
    time.sleep(2)
    
    return True

def test_emotion_alert_threshold(ip, port):
    """测试情绪警报阈值（4次触发）"""
    print("\n📊 测试1: 情绪警报阈值（4次触发）")
    print("-" * 40)
    
    # 发送5次Sad情绪，第5次应该触发语音警报
    emotions = ["Sad", "Sad", "Sad", "Sad", "Sad"]
    
    for i, emotion in enumerate(emotions, 1):
        print(f"📤 发送第{i}次数据: {emotion}")
        send_data(ip, port, emotion, "Good Posture")
        
        if i == 4:
            print("⚠️  注意：前4次不应该触发警报")
        elif i == 5:
            print("🚨 预期：第5次应该触发语音警报！")
            print("👂 请听小智是否播放了语音提醒...")
            print("   应该听到：'我注意到你最近看起来有些难过，要不要聊聊天？'")
        
        time.sleep(3)
    
    print("✅ 情绪警报阈值测试完成")

def test_posture_alert_threshold(ip, port):
    """测试坐姿警报阈值（4次触发）"""
    print("\n🪑 测试2: 坐姿警报阈值（4次触发）")
    print("-" * 40)
    
    # 等待冷却时间
    print("⏰ 等待30秒冷却时间...")
    time.sleep(30)
    
    # 发送5次Poor Posture，第5次应该触发语音警报
    postures = ["Poor Posture", "Poor Posture", "Poor Posture", "Poor Posture", "Poor Posture"]
    
    for i, posture in enumerate(postures, 1):
        print(f"📤 发送第{i}次数据: {posture}")
        send_data(ip, port, "Neutral", posture)
        
        if i == 4:
            print("⚠️  注意：前4次不应该触发警报")
        elif i == 5:
            print("🚨 预期：第5次应该触发语音警报！")
            print("👂 请听小智是否播放了语音提醒...")
            print("   应该听到：'你已经保持不良坐姿一段时间了，记得调整一下坐姿哦！'")
        
        time.sleep(3)
    
    print("✅ 坐姿警报阈值测试完成")

def test_mixed_data_scenario(ip, port):
    """测试混合数据场景"""
    print("\n🔄 测试3: 混合数据场景")
    print("-" * 40)
    
    # 等待冷却时间
    print("⏰ 等待30秒冷却时间...")
    time.sleep(30)
    
    # 发送混合数据：Happy, Angry, Calm, Angry, Angry
    # Angry出现3次，但总共5次数据，应该在第5次触发
    data_sequence = [
        ("Happy", "Good Posture"),
        ("Angry", "Good Posture"),
        ("Calm", "Good Posture"),
        ("Angry", "Good Posture"),
        ("Angry", "Good Posture")
    ]
    
    for i, (emotion, posture) in enumerate(data_sequence, 1):
        print(f"📤 发送第{i}次数据: {emotion}")
        send_data(ip, port, emotion, posture)
        
        if i == 5:
            print("🚨 预期：Angry出现3次，应该触发语音警报！")
            print("👂 请听小智是否播放了语音提醒...")
            print("   应该听到：'你看起来有点生气，深呼吸一下，放松心情吧。'")
        
        time.sleep(3)
    
    print("✅ 混合数据场景测试完成")

def test_cooldown_mechanism(ip, port):
    """测试冷却机制（30秒）"""
    print("\n⏰ 测试4: 冷却机制（30秒）")
    print("-" * 40)
    
    print("📤 立即再次发送5次Sad数据...")
    # 立即再次发送相同数据，应该被冷却机制阻止
    for i in range(5):
        send_data(ip, port, "Sad", "Good Posture")
        time.sleep(1)
    
    print("🚫 预期：应该被冷却机制阻止，不会触发警报")
    print("⏰ 冷却时间为30秒")
    
    print("✅ 冷却机制测试完成")

def test_force_wakeup_scenario(ip, port):
    """测试强制唤醒场景"""
    print("\n🎯 测试5: 强制唤醒场景")
    print("-" * 40)
    print("说明：这个测试需要在小智正在说话或听话时进行")
    print("如果小智当前是空闲状态，请先手动让它进入说话或听话状态")
    
    input("按回车键继续测试强制唤醒功能...")
    
    # 发送5次数据触发强制唤醒
    for i in range(5):
        print(f"📤 发送第{i+1}次数据: Angry")
        send_data(ip, port, "Angry", "Good Posture")
        time.sleep(2)
    
    print("🚨 预期：小智应该被强制唤醒并播放语音警报")
    print("✅ 强制唤醒测试完成")

def main():
    parser = argparse.ArgumentParser(description="测试小智语音警报修复效果")
    parser.add_argument("ip", help="小智设备的IP地址")
    parser.add_argument("-p", "--port", type=int, default=8080, help="端口号 (默认: 8080)")
    parser.add_argument("-t", "--test", choices=["all", "emotion", "posture", "mixed", "cooldown", "wakeup"],
                       default="all", help="选择测试类型")
    
    args = parser.parse_args()
    
    print("🤖 小智语音警报修复测试工具")
    print("=" * 60)
    
    # 测试连接
    if not test_voice_alert_fix(args.ip, args.port):
        return
    
    try:
        if args.test == "all":
            test_emotion_alert_threshold(args.ip, args.port)
            test_posture_alert_threshold(args.ip, args.port)
            test_mixed_data_scenario(args.ip, args.port)
            test_cooldown_mechanism(args.ip, args.port)
            test_force_wakeup_scenario(args.ip, args.port)
        elif args.test == "emotion":
            test_emotion_alert_threshold(args.ip, args.port)
        elif args.test == "posture":
            test_posture_alert_threshold(args.ip, args.port)
        elif args.test == "mixed":
            test_mixed_data_scenario(args.ip, args.port)
        elif args.test == "cooldown":
            test_cooldown_mechanism(args.ip, args.port)
        elif args.test == "wakeup":
            test_force_wakeup_scenario(args.ip, args.port)
        
        print("\n🎉 所有测试完成！")
        print("\n📋 验证清单：")
        print("✓ 警报阈值是否从3次改为4次？")
        print("✓ 超过4次时是否自动唤醒小智？")
        print("✓ 是否播放了真正的语音提醒？")
        print("✓ 是否不再只播放震动声音？")
        print("✓ 冷却时间是否为30秒？")
        print("✓ 强制唤醒功能是否正常？")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
