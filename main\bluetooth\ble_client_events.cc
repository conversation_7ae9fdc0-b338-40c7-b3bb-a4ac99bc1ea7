// BLE客户端事件处理方法的扩展实现
#include "ble_client.h"
#include <cstring>

static const char* TAG = "BleClient";

void BleClient::HandleGattcRegisterEvent(esp_ble_gattc_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTC register event, status: %d, app_id: %d", 
             param->reg.status, param->reg.app_id);
    
    if (param->reg.status == ESP_GATT_OK) {
        // 设置设备名称
        esp_ble_gap_set_device_name(device_name_.c_str());
        ESP_LOGI(TAG, "GATT client registered successfully");
    }
}

void BleClient::HandleGattcOpenEvent(esp_ble_gattc_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTC open event, status: %d, conn_id: %d", 
             param->open.status, param->open.conn_id);
    
    if (param->open.status == ESP_GATT_OK) {
        conn_id_ = param->open.conn_id;
        connection_state_ = BLE_CONNECTED;
        
        ESP_LOGI(TAG, "Connected to device successfully");
        
        // 开始搜索服务
        esp_ble_gattc_search_service(gattc_if_, conn_id_, nullptr);
        
        // 通知连接状态变化
        if (connection_callback_) {
            connection_callback_(connection_state_, connected_device_);
        }
    } else {
        ESP_LOGE(TAG, "Failed to connect to device");
        connection_state_ = BLE_DISCONNECTED;
        
        if (connection_callback_) {
            connection_callback_(connection_state_, connected_device_);
        }
    }
}

void BleClient::HandleGattcCloseEvent(esp_ble_gattc_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTC close event, conn_id: %d", param->close.conn_id);
    
    connection_state_ = BLE_DISCONNECTED;
    conn_id_ = 0;
    service_start_handle_ = 0;
    service_end_handle_ = 0;
    char_handle_ = 0;
    memset(connected_device_, 0, sizeof(esp_bd_addr_t));
    
    ESP_LOGI(TAG, "Disconnected from device");
    
    // 通知连接状态变化
    if (connection_callback_) {
        connection_callback_(connection_state_, connected_device_);
    }
}

void BleClient::HandleGattcSearchResultEvent(esp_ble_gattc_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTC search result, service UUID: 0x%04x, start_handle: %d, end_handle: %d",
             param->search_res.srvc_id.uuid.uuid.uuid16,
             param->search_res.start_handle,
             param->search_res.end_handle);
    
    // 检查是否是我们要找的服务
    if (param->search_res.srvc_id.uuid.len == ESP_UUID_LEN_16 &&
        param->search_res.srvc_id.uuid.uuid.uuid16 == TARGET_SERVICE_UUID) {
        
        service_start_handle_ = param->search_res.start_handle;
        service_end_handle_ = param->search_res.end_handle;
        
        ESP_LOGI(TAG, "Found target service");
    }
}

void BleClient::HandleGattcSearchCompleteEvent(esp_ble_gattc_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTC search complete, status: %d", param->search_cmpl.status);

    if (param->search_cmpl.status == ESP_GATT_OK && service_start_handle_ != 0) {
        // 搜索特征 - 使用正确的API
        esp_bt_uuid_t char_uuid;
        char_uuid.len = ESP_UUID_LEN_16;
        char_uuid.uuid.uuid16 = TARGET_CHAR_UUID;

        // 直接读取特征来获取句柄
        esp_err_t ret = esp_ble_gattc_read_char(gattc_if_, conn_id_, service_start_handle_ + 1, ESP_GATT_AUTH_REQ_NONE);
        if (ret == ESP_OK) {
            char_handle_ = service_start_handle_ + 1; // 假设特征句柄是服务句柄+1
            ESP_LOGI(TAG, "Assumed characteristic handle: %d", char_handle_);
        } else {
            ESP_LOGE(TAG, "Failed to read characteristic");
        }
    } else {
        ESP_LOGE(TAG, "Target service not found");
    }
}

void BleClient::HandleGattcGetCharEvent(esp_ble_gattc_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTC read char event, status: %d, handle: %d",
             param->read.status, param->read.handle);

    if (param->read.status == ESP_GATT_OK) {
        char_handle_ = param->read.handle;

        ESP_LOGI(TAG, "Found target characteristic, handle: %d", char_handle_);

        // 注册通知
        esp_ble_gattc_register_for_notify(gattc_if_, connected_device_, char_handle_);
    } else {
        ESP_LOGE(TAG, "Target characteristic not found");
    }
}

void BleClient::HandleGattcNotifyEvent(esp_ble_gattc_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTC notify event, handle: %d, len: %d", 
             param->notify.handle, param->notify.value_len);
    
    if (param->notify.handle == char_handle_) {
        // 处理接收到的数据
        if (data_callback_) {
            data_callback_(param->notify.value, param->notify.value_len);
        }
    }
}

void BleClient::HandleGattcWriteEvent(esp_ble_gattc_cb_param_t* param) {
    ESP_LOGI(TAG, "GATTC write event, status: %d, handle: %d", 
             param->write.status, param->write.handle);
    
    if (param->write.status != ESP_GATT_OK) {
        ESP_LOGE(TAG, "Write characteristic failed");
    } else {
        ESP_LOGD(TAG, "Write characteristic success");
    }
}
