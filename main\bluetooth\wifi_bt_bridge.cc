#include "wifi_bt_bridge.h"
#include "esp_log.h"
#include <cstring>

static const char* TAG = "WiFiBtBridge";

WiFiBtBridge::WiFiBtBridge(const std::string& bt_device_name, uint16_t wifi_port, int max_wifi_clients)
    : bt_server_(nullptr)
    , wifi_receiver_initialized_(false)
    , bt_server_initialized_(false)
    , bridge_running_(false)
    , bt_device_name_(bt_device_name)
    , wifi_port_(wifi_port)
    , max_wifi_clients_(max_wifi_clients)
    , wifi_packets_received_(0)
    , bt_packets_sent_(0)
    , bt_packets_failed_(0) {
    
    ESP_LOGI(TAG, "WiFi-Bluetooth Bridge created");
    ESP_LOGI(TAG, "BT Device Name: %s", bt_device_name_.c_str());
    ESP_LOGI(TAG, "WiFi Port: %d", wifi_port_);
    ESP_LOGI(TAG, "Max WiFi Clients: %d", max_wifi_clients_);
}

WiFiBtBridge::~WiFiBtBridge() {
    Deinitialize();
    ESP_LOGI(TAG, "WiFi-Bluetooth Bridge destroyed");
}

bool WiFiBtBridge::Initialize() {
    ESP_LOGI(TAG, "Initializing WiFi-Bluetooth Bridge");
    
    // 初始化WiFi接收器
    if (!InitializeWifiReceiver()) {
        ESP_LOGE(TAG, "Failed to initialize WiFi receiver");
        return false;
    }
    
    // 初始化蓝牙服务器
    if (!InitializeBluetoothServer()) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth server");
        return false;
    }
    
    ESP_LOGI(TAG, "WiFi-Bluetooth Bridge initialized successfully");
    return true;
}

bool WiFiBtBridge::Start() {
    if (bridge_running_) {
        ESP_LOGW(TAG, "Bridge already running");
        return true;
    }
    
    ESP_LOGI(TAG, "Starting WiFi-Bluetooth Bridge");
    
    // 启动WiFi接收器
    esp_err_t ret = wifi_data_receiver_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start WiFi receiver: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 启动蓝牙服务器
    if (!bt_server_->StartServer()) {
        ESP_LOGE(TAG, "Failed to start Bluetooth server");
        wifi_data_receiver_stop();
        return false;
    }
    
    // 设置蓝牙为可发现和可连接
    bt_server_->SetDiscoverable(true);
    bt_server_->SetConnectable(true);
    
    bridge_running_ = true;
    SendStatusMessage("WiFi-Bluetooth Bridge started successfully");
    
    ESP_LOGI(TAG, "WiFi-Bluetooth Bridge started successfully");
    return true;
}

bool WiFiBtBridge::Stop() {
    if (!bridge_running_) {
        ESP_LOGW(TAG, "Bridge not running");
        return true;
    }
    
    ESP_LOGI(TAG, "Stopping WiFi-Bluetooth Bridge");
    
    // 停止WiFi接收器
    wifi_data_receiver_stop();
    
    // 停止蓝牙服务器
    if (bt_server_) {
        bt_server_->StopServer();
    }
    
    bridge_running_ = false;
    SendStatusMessage("WiFi-Bluetooth Bridge stopped");
    
    ESP_LOGI(TAG, "WiFi-Bluetooth Bridge stopped");
    return true;
}

void WiFiBtBridge::Deinitialize() {
    Stop();
    
    // 反初始化WiFi接收器
    if (wifi_receiver_initialized_) {
        wifi_data_receiver_deinit();
        wifi_receiver_initialized_ = false;
    }
    
    // 反初始化蓝牙服务器
    if (bt_server_) {
        delete bt_server_;
        bt_server_ = nullptr;
        bt_server_initialized_ = false;
    }
    
    ESP_LOGI(TAG, "WiFi-Bluetooth Bridge deinitialized");
}

void WiFiBtBridge::SetStatusCallback(status_callback_t callback) {
    status_callback_ = callback;
}

void WiFiBtBridge::SetDataForwardCallback(data_forward_callback_t callback) {
    data_forward_callback_ = callback;
}

bool WiFiBtBridge::IsBtClientConnected() const {
    return bt_server_ && bt_server_->IsClientConnected();
}

int WiFiBtBridge::GetWifiClientCount() const {
    return wifi_data_receiver_get_client_count();
}

void WiFiBtBridge::GetStatistics(uint32_t& wifi_received, uint32_t& bt_sent, uint32_t& bt_failed) const {
    wifi_received = wifi_packets_received_;
    bt_sent = bt_packets_sent_;
    bt_failed = bt_packets_failed_;
}

void WiFiBtBridge::ResetStatistics() {
    wifi_packets_received_ = 0;
    bt_packets_sent_ = 0;
    bt_packets_failed_ = 0;
    ESP_LOGI(TAG, "Statistics reset");
}

bool WiFiBtBridge::InitializeWifiReceiver() {
    ESP_LOGI(TAG, "Initializing WiFi receiver");
    
    // 获取默认配置
    wifi_data_receiver_config_t config = wifi_data_receiver_get_default_config();
    config.port = wifi_port_;
    config.max_clients = max_wifi_clients_;
    config.recv_timeout_ms = 5000;
    config.auto_start = false;  // 手动启动
    
    // 初始化WiFi接收器
    esp_err_t ret = wifi_data_receiver_init(&config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize WiFi receiver: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 设置回调函数
    wifi_data_receiver_set_data_callback(WifiDataReceivedCallback, this);
    wifi_data_receiver_set_client_callback(WifiClientStatusCallback, this);
    
    wifi_receiver_initialized_ = true;
    ESP_LOGI(TAG, "WiFi receiver initialized successfully");
    return true;
}

bool WiFiBtBridge::InitializeBluetoothServer() {
    ESP_LOGI(TAG, "Initializing Bluetooth server");
    
    // 创建蓝牙服务器实例
    bt_server_ = new BluetoothServer(bt_device_name_);
    if (!bt_server_) {
        ESP_LOGE(TAG, "Failed to create Bluetooth server instance");
        return false;
    }
    
    // 设置连接状态回调
    bt_server_->SetConnectionStatusCallback(
        [this](bool connected, const esp_bd_addr_t& address) {
            BluetoothConnectionCallback(connected, address);
        }
    );
    
    // 初始化蓝牙服务器
    if (!bt_server_->Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth server");
        delete bt_server_;
        bt_server_ = nullptr;
        return false;
    }
    
    bt_server_initialized_ = true;
    ESP_LOGI(TAG, "Bluetooth server initialized successfully");
    return true;
}

void WiFiBtBridge::WifiDataReceivedCallback(const wifi_data_packet_t* packet, void* user_data) {
    WiFiBtBridge* bridge = static_cast<WiFiBtBridge*>(user_data);
    if (bridge) {
        bridge->HandleWifiData(packet);
    }
}

void WiFiBtBridge::WifiClientStatusCallback(const char* client_ip, uint16_t client_port, 
                                          bool connected, void* user_data) {
    WiFiBtBridge* bridge = static_cast<WiFiBtBridge*>(user_data);
    if (bridge) {
        bridge->HandleWifiClientStatus(client_ip, client_port, connected);
    }
}

void WiFiBtBridge::BluetoothConnectionCallback(bool connected, const esp_bd_addr_t& address) {
    if (connected) {
        ESP_LOGI(TAG, "Bluetooth client connected: %02x:%02x:%02x:%02x:%02x:%02x",
                 address[0], address[1], address[2], address[3], address[4], address[5]);
        SendStatusMessage("Bluetooth client connected");
    } else {
        ESP_LOGI(TAG, "Bluetooth client disconnected");
        SendStatusMessage("Bluetooth client disconnected");
    }
}

void WiFiBtBridge::HandleWifiData(const wifi_data_packet_t* packet) {
    if (!packet || !packet->data) {
        ESP_LOGW(TAG, "Invalid WiFi data packet");
        return;
    }
    
    wifi_packets_received_++;
    
    ESP_LOGD(TAG, "WiFi data received from %s:%d, length: %d", 
             packet->client_ip, packet->client_port, packet->length);
    
    // 构建数据源描述
    char source[64];
    snprintf(source, sizeof(source), "WiFi:%s:%d", packet->client_ip, packet->client_port);
    
    // 通过蓝牙转发数据
    if (ForwardDataViaBluetooth((const uint8_t*)packet->data, packet->length, source)) {
        bt_packets_sent_++;
        ESP_LOGD(TAG, "Data forwarded via Bluetooth successfully");
        
        if (data_forward_callback_) {
            data_forward_callback_(std::string(packet->data, packet->length), source);
        }
    } else {
        bt_packets_failed_++;
        ESP_LOGW(TAG, "Failed to forward data via Bluetooth");
    }
}

void WiFiBtBridge::HandleWifiClientStatus(const char* client_ip, uint16_t client_port, bool connected) {
    if (connected) {
        ESP_LOGI(TAG, "WiFi client connected: %s:%d", client_ip, client_port);
        SendStatusMessage("WiFi client connected: " + std::string(client_ip));
    } else {
        ESP_LOGI(TAG, "WiFi client disconnected: %s:%d", client_ip, client_port);
        SendStatusMessage("WiFi client disconnected: " + std::string(client_ip));
    }
}

bool WiFiBtBridge::ForwardDataViaBluetooth(const uint8_t* data, size_t length, const std::string& source) {
    if (!bt_server_ || !bt_server_->IsClientConnected()) {
        ESP_LOGD(TAG, "No Bluetooth client connected, cannot forward data");
        return false;
    }
    
    // 创建带有源信息的数据包
    std::string packet = "[" + source + "] ";
    packet.append((const char*)data, length);
    
    return bt_server_->SendString(packet);
}

void WiFiBtBridge::SendStatusMessage(const std::string& message) {
    ESP_LOGI(TAG, "Status: %s", message.c_str());
    
    if (status_callback_) {
        status_callback_(message);
    }
}
