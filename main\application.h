#ifndef _APPLICATION_H_
#define _APPLICATION_H_

#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <freertos/task.h>
#include <esp_timer.h>

#include <string>
#include <mutex>
#include <list>
#include <vector>
#include <condition_variable>
#include <memory>
#include <deque>

// Use the custom opus encoder wrapper instead of standard opus headers
#include <opus_encoder.h>
#include <opus_decoder.h>
#include <opus_resampler.h>

#include "protocol.h"
#include "ota.h"
#include "background_task.h"
#include "audio_processor.h"
#include "wake_word.h"
#include "audio_debugger.h"
#include "bluetooth/ble_client.h"

#ifdef __cplusplus
extern "C" {
#endif
#include "wifi_data_receiver.h"
#ifdef __cplusplus
}
#endif

#define SCHEDULE_EVENT (1 << 0)
#define SEND_AUDIO_EVENT (1 << 1)
#define CHECK_NEW_VERSION_DONE_EVENT (1 << 2)

enum AecMode {
    kAecOff,
    kAecOnDeviceSide,
    kAecOnServerSide,
};

enum DeviceState {
    kDeviceStateUnknown,
    kDeviceStateStarting,
    kDeviceStateWifiConfiguring,
    kDeviceStateIdle,
    kDeviceStateConnecting,
    kDeviceStateListening,
    kDeviceStateSpeaking,
    kDeviceStateUpgrading,
    kDeviceStateActivating,
    kDeviceStateAudioTesting,
    kDeviceStateFatalError
};

#define OPUS_FRAME_DURATION_MS 60
#define MAX_AUDIO_PACKETS_IN_QUEUE (2400 / OPUS_FRAME_DURATION_MS)
#define AUDIO_TESTING_MAX_DURATION_MS 10000

class Application {
public:
    static Application& GetInstance() {
        static Application instance;
        return instance;
    }
    // 删除拷贝构造函数和赋值运算符
    Application(const Application&) = delete;
    Application& operator=(const Application&) = delete;

    void Start();
    DeviceState GetDeviceState() const { return device_state_; }
    bool IsVoiceDetected() const { return voice_detected_; }
    void Schedule(std::function<void()> callback);
    void SetDeviceState(DeviceState state);
    void Alert(const char* status, const char* message, const char* emotion = "", const std::string_view& sound = "");
    void DismissAlert();
    void AbortSpeaking(AbortReason reason);
    void ToggleChatState();
    void StartListening();
    void StopListening();
    void UpdateIotStates();
    void Reboot();
    void WakeWordInvoke(const std::string& wake_word);
    void PlaySound(const std::string_view& sound);
    void PlaySoundImmediate(const std::string_view& sound);
    void ForceWakeUpForAlert();  // 新增：强制唤醒用于警报
    bool CanEnterSleepMode();
    void SendMcpMessage(const std::string& payload);
    void SendTtsRequest(const std::string& text);
    void SetAecMode(AecMode mode);
    bool ReadAudio(std::vector<int16_t>& data, int sample_rate, int samples);
    AecMode GetAecMode() const { return aec_mode_; }
    BackgroundTask* GetBackgroundTask() const { return background_task_; }

    // MCP工具方法
    std::string GetCurrentEmotionStatus();
    std::string GetCurrentPostureStatus();

private:
    Application();
    ~Application();

    std::unique_ptr<WakeWord> wake_word_;
    std::unique_ptr<AudioProcessor> audio_processor_;
    std::unique_ptr<AudioDebugger> audio_debugger_;
    std::mutex mutex_;
    std::list<std::function<void()>> main_tasks_;
    std::unique_ptr<Protocol> protocol_;
    EventGroupHandle_t event_group_ = nullptr;
    esp_timer_handle_t clock_timer_handle_ = nullptr;
    volatile DeviceState device_state_ = kDeviceStateUnknown;
    ListeningMode listening_mode_ = kListeningModeAutoStop;
    AecMode aec_mode_ = kAecOff;

    bool has_server_time_ = false;
    bool aborted_ = false;
    bool voice_detected_ = false;
    bool busy_decoding_audio_ = false;
    int clock_ticks_ = 0;
    TaskHandle_t check_new_version_task_handle_ = nullptr;

    // Audio encode / decode
    TaskHandle_t audio_loop_task_handle_ = nullptr;
    BackgroundTask* background_task_ = nullptr;
    std::chrono::steady_clock::time_point last_output_time_;
    std::list<AudioStreamPacket> audio_send_queue_;
    std::list<AudioStreamPacket> audio_decode_queue_;
    std::condition_variable audio_decode_cv_;
    std::list<AudioStreamPacket> audio_testing_queue_;

    // 新增：用于维护音频包的timestamp队列
    std::list<uint32_t> timestamp_queue_;
    std::mutex timestamp_mutex_;

    std::unique_ptr<OpusEncoderWrapper> opus_encoder_;
    std::unique_ptr<OpusDecoderWrapper> opus_decoder_;

    OpusResampler input_resampler_;
    OpusResampler reference_resampler_;
    OpusResampler output_resampler_;

    // WiFi数据接收相关
    bool wifi_data_receiver_enabled_ = false;
    bool wifi_data_receiver_initialized_ = false;

    // BLE相关成员
    BleClient* ble_client_ = nullptr;
    bool ble_client_initialized_ = false;
    bool ble_connected_ = false;
    esp_bd_addr_t target_device_address_;

    // 数据统计相关
    struct DataHistory {
        std::deque<std::string> emotion_history;
        std::deque<std::string> posture_history;
        std::string last_emotion_alert;
        std::string last_posture_alert;
        uint32_t last_alert_time = 0;
        static const size_t MAX_HISTORY_SIZE = 5;
        static const size_t ALERT_THRESHOLD = 4;  // 修改：超过4次才触发警报
        static const uint32_t ALERT_COOLDOWN_MS = 30000; // 修改：30秒冷却时间
    };
    DataHistory data_history_;
    bool is_critical_alert_active_ = false;  // 新增：标记是否有紧急警报

    void MainEventLoop();
    void OnAudioInput();
    void OnAudioOutput();
    void ResetDecoder();
    void SetDecodeSampleRate(int sample_rate, int frame_duration);
    void CheckNewVersion(Ota& ota);
    void ShowActivationCode(const std::string& code, const std::string& message);
    void OnClockTimer();
    void SetListeningMode(ListeningMode mode);
    void AudioLoop();
    void EnterAudioTestingMode();
    void ExitAudioTestingMode();

    // WiFi数据接收相关方法
    void InitializeWifiDataReceiver();
    void StartWifiDataReceiver();
    void StopWifiDataReceiver();
    static void OnWifiDataReceived(const wifi_data_packet_t* packet, void* user_data);
    static void OnWifiClientStatus(const char* client_ip, uint16_t client_port, bool connected, void* user_data);

    // BLE相关方法
    void InitializeBleClient();
    void StartBleDeviceSearch();
    void ConnectToTargetDevice();
    bool SendDataViaBle(const uint8_t* data, size_t length);
    void OnBleDataReceived(const uint8_t* data, size_t length);
    void OnBleConnectionChanged(BleConnectionState state, const esp_bd_addr_t& address);

    // 数据统计和智能提醒方法
    void ProcessEmotionData(const std::string& emotion);
    void ProcessPostureData(const std::string& posture);
    bool ShouldTriggerAlert(const std::deque<std::string>& history, const std::string& target_state);
    void TriggerSmartAlert(const std::string& type, const std::string& state);
};

#endif // _APPLICATION_H_
