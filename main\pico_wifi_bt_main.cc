#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "bluetooth/wifi_bt_bridge.h"
#include "esp_netif.h"

static const char* TAG = "PICO_MAIN";

// WiFi事件组
static EventGroupHandle_t s_wifi_event_group;
#define WIFI_CONNECTED_BIT BIT0
#define WIFI_FAIL_BIT      BIT1

// WiFi配置
#define WIFI_SSID      "YOUR_WIFI_SSID"      // 请修改为你的WiFi名称
#define WIFI_PASS      "YOUR_WIFI_PASSWORD"  // 请修改为你的WiFi密码
#define WIFI_MAXIMUM_RETRY  5

// 全局变量
static WiFiBtBridge* g_bridge = nullptr;
static int s_retry_num = 0;

/**
 * @brief WiFi事件处理函数
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data) {
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        if (s_retry_num < WIFI_MAXIMUM_RETRY) {
            esp_wifi_connect();
            s_retry_num++;
            ESP_LOGI(TAG, "retry to connect to the AP");
        } else {
            xEventGroupSetBits(s_wifi_event_group, WIFI_FAIL_BIT);
        }
        ESP_LOGI(TAG, "connect to the AP fail");
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "got ip:" IPSTR, IP2STR(&event->ip_info.ip));
        s_retry_num = 0;
        xEventGroupSetBits(s_wifi_event_group, WIFI_CONNECTED_BIT);
    }
}

/**
 * @brief 初始化WiFi
 */
static bool wifi_init_sta(void) {
    s_wifi_event_group = xEventGroupCreate();

    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    esp_event_handler_instance_t instance_any_id;
    esp_event_handler_instance_t instance_got_ip;
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_any_id));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_got_ip));

    wifi_config_t wifi_config = {};
    strcpy((char*)wifi_config.sta.ssid, WIFI_SSID);
    strcpy((char*)wifi_config.sta.password, WIFI_PASS);
    wifi_config.sta.threshold.authmode = WIFI_AUTH_WPA2_PSK;

    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "wifi_init_sta finished.");

    /* Waiting until either the connection is established (WIFI_CONNECTED_BIT) or connection failed for the maximum
     * number of re-tries (WIFI_FAIL_BIT). The bits are set by event_handler() (see above) */
    EventBits_t bits = xEventGroupWaitBits(s_wifi_event_group,
            WIFI_CONNECTED_BIT | WIFI_FAIL_BIT,
            pdFALSE,
            pdFALSE,
            portMAX_DELAY);

    /* xEventGroupWaitBits() returns the bits before the call returned, hence we can test which event actually
     * happened. */
    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "connected to ap SSID:%s password:%s", WIFI_SSID, WIFI_PASS);
        return true;
    } else if (bits & WIFI_FAIL_BIT) {
        ESP_LOGI(TAG, "Failed to connect to SSID:%s, password:%s", WIFI_SSID, WIFI_PASS);
        return false;
    } else {
        ESP_LOGE(TAG, "UNEXPECTED EVENT");
        return false;
    }
}

/**
 * @brief 状态监控任务
 */
static void status_monitor_task(void* pvParameters) {
    uint32_t last_wifi_received = 0;
    uint32_t last_bt_sent = 0;
    uint32_t last_bt_failed = 0;
    
    while (true) {
        if (g_bridge && g_bridge->IsRunning()) {
            uint32_t wifi_received, bt_sent, bt_failed;
            g_bridge->GetStatistics(wifi_received, bt_sent, bt_failed);
            
            // 检查是否有新的数据传输
            if (wifi_received != last_wifi_received || 
                bt_sent != last_bt_sent || 
                bt_failed != last_bt_failed) {
                
                ESP_LOGI(TAG, "Statistics - WiFi RX: %lu, BT TX: %lu, BT Failed: %lu",
                         wifi_received, bt_sent, bt_failed);
                
                last_wifi_received = wifi_received;
                last_bt_sent = bt_sent;
                last_bt_failed = bt_failed;
            }
            
            // 显示连接状态
            ESP_LOGI(TAG, "Status - WiFi Clients: %d, BT Connected: %s",
                     g_bridge->GetWifiClientCount(),
                     g_bridge->IsBtClientConnected() ? "Yes" : "No");
        }
        
        vTaskDelay(pdMS_TO_TICKS(10000)); // 每10秒检查一次
    }
}

/**
 * @brief 桥接器状态回调
 */
static void bridge_status_callback(const std::string& message) {
    ESP_LOGI(TAG, "Bridge Status: %s", message.c_str());
}

/**
 * @brief 数据转发回调
 */
static void data_forward_callback(const std::string& data, const std::string& source) {
    ESP_LOGD(TAG, "Data forwarded from %s: %s", source.c_str(), data.c_str());
}

/**
 * @brief 主函数
 */
extern "C" void app_main(void) {
    ESP_LOGI(TAG, "ESP32-S3 PICO-1 WiFi-Bluetooth Bridge Starting...");
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 初始化WiFi
    ESP_LOGI(TAG, "Initializing WiFi...");
    if (!wifi_init_sta()) {
        ESP_LOGE(TAG, "Failed to connect to WiFi");
        return;
    }
    
    // 获取并显示IP地址
    esp_netif_t* netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
    esp_netif_ip_info_t ip_info;
    esp_netif_get_ip_info(netif, &ip_info);
    ESP_LOGI(TAG, "WiFi connected. IP: " IPSTR, IP2STR(&ip_info.ip));
    
    // 创建WiFi-蓝牙桥接器
    ESP_LOGI(TAG, "Creating WiFi-Bluetooth Bridge...");
    g_bridge = new WiFiBtBridge("ESP32-S3-PICO-Bridge", 8080, 5);
    
    // 设置回调函数
    g_bridge->SetStatusCallback(bridge_status_callback);
    g_bridge->SetDataForwardCallback(data_forward_callback);
    
    // 初始化桥接器
    if (!g_bridge->Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize bridge");
        delete g_bridge;
        return;
    }
    
    // 启动桥接器
    if (!g_bridge->Start()) {
        ESP_LOGE(TAG, "Failed to start bridge");
        delete g_bridge;
        return;
    }
    
    ESP_LOGI(TAG, "WiFi-Bluetooth Bridge started successfully!");
    ESP_LOGI(TAG, "WiFi Server listening on " IPSTR ":8080", IP2STR(&ip_info.ip));
    ESP_LOGI(TAG, "Bluetooth device name: ESP32-S3-PICO-Bridge");
    ESP_LOGI(TAG, "Ready to receive WiFi data and forward via Bluetooth");
    
    // 创建状态监控任务
    xTaskCreate(status_monitor_task, "status_monitor", 4096, NULL, 5, NULL);
    
    // 主循环
    while (true) {
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 这里可以添加其他周期性任务
        // 例如：LED指示、按键检测等
    }
    
    // 清理资源（通常不会执行到这里）
    if (g_bridge) {
        g_bridge->Stop();
        delete g_bridge;
        g_bridge = nullptr;
    }
}
