# Fixed ESP-IDF Build Script
# This script uses the correct ESP-IDF environment setup

Write-Host "========================================" -ForegroundColor Green
Write-Host "Fixed ESP-IDF Build Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "CMakeLists.txt")) {
    Write-Host "Error: CMakeLists.txt not found. Please run this script from the project root directory." -ForegroundColor Red
    exit 1
}

# Set ESP-IDF paths based on the working environment
$IDF_PATH = "C:\Users\<USER>\esp\v5.4.2\esp-idf"
$PYTHON_ENV = "D:\esp32-idf-ahy\5.3.2\python_env\idf5.3_py3.11_env"
$TOOLS_PATH = "D:\esp32-idf-ahy\5.3.2\tools"

Write-Host "Using ESP-IDF at: $IDF_PATH" -ForegroundColor Cyan
Write-Host "Using Python env: $PYTHON_ENV" -ForegroundColor Cyan

# Check if paths exist
if (-not (Test-Path $IDF_PATH)) {
    Write-Host "Error: ESP-IDF not found at $IDF_PATH" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path "$PYTHON_ENV\Scripts\python.exe")) {
    Write-Host "Error: Python environment not found at $PYTHON_ENV" -ForegroundColor Red
    exit 1
}

# Set environment variables
$env:IDF_PATH = $IDF_PATH
$env:PATH = "$TOOLS_PATH;$PYTHON_ENV\Scripts;C:\Users\<USER>\Desktop\shijue\tools\idf-git\2.39.2\cmd;" + $env:PATH

Write-Host "Environment setup completed" -ForegroundColor Green

# Clean build directory if it exists
if (Test-Path "build") {
    Write-Host "Cleaning existing build directory..." -ForegroundColor Yellow
    try {
        Remove-Item "build" -Recurse -Force
        Write-Host "✓ Build directory cleaned" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Could not clean build directory: $_" -ForegroundColor Yellow
    }
}

# Start the build
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Starting Build" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

try {
    $pythonExe = "$PYTHON_ENV\Scripts\python.exe"
    $idfPy = "$IDF_PATH\tools\idf.py"
    
    Write-Host "Executing: $pythonExe $idfPy build" -ForegroundColor Cyan
    
    # Run the build command with verbose output
    & $pythonExe $idfPy build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n========================================" -ForegroundColor Green
        Write-Host "Build completed successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        
        # Show build artifacts
        if (Test-Path "build\xiaozhi.bin") {
            $binSize = (Get-Item "build\xiaozhi.bin").Length
            Write-Host "Firmware binary: build\xiaozhi.bin ($([math]::Round($binSize/1KB, 2)) KB)" -ForegroundColor Cyan
        }
        
        if (Test-Path "build") {
            Write-Host "Build directory contents:" -ForegroundColor Yellow
            Get-ChildItem "build" -Name | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        }
        
    } else {
        Write-Host "`nBuild failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "`nBuild failed with exception: $_" -ForegroundColor Red
    exit 1
}
