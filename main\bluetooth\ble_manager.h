#ifndef BLE_MANAGER_H
#define BLE_MANAGER_H

#include <string>
#include <vector>
#include <functional>
#include "esp_gap_ble_api.h"
#include "esp_gatts_api.h"
#include "esp_gattc_api.h"
#include "esp_bt_main.h"
#include "esp_bt.h"
#include "esp_log.h"

// BLE连接状态枚举
enum BleConnectionState {
    BLE_DISCONNECTED = 0,
    BLE_CONNECTING,
    BLE_CONNECTED,
    BLE_DISCONNECTING
};

// BLE设备信息结构
struct BleDeviceInfo {
    esp_bd_addr_t address;
    std::string name;
    int rssi;
    uint8_t adv_data[ESP_BLE_ADV_DATA_LEN_MAX];
    uint8_t adv_data_len;
    uint8_t scan_rsp_data[ESP_BLE_SCAN_RSP_DATA_LEN_MAX];
    uint8_t scan_rsp_len;
};

// BLE数据回调函数类型
using BleDataCallback = std::function<void(const uint8_t* data, size_t length)>;
using BleConnectionCallback = std::function<void(BleConnectionState state, const esp_bd_addr_t& address)>;

// BLE管理器基类
class BleManager {
public:
    virtual ~BleManager() = default;
    
    // 基本操作
    virtual bool Initialize() = 0;
    virtual bool Deinitialize() = 0;
    virtual bool IsInitialized() const = 0;
    
    // 连接管理
    virtual BleConnectionState GetConnectionState() const = 0;
    virtual bool IsConnected() const { return GetConnectionState() == BLE_CONNECTED; }
    
    // 数据传输
    virtual bool SendData(const uint8_t* data, size_t length) = 0;
    virtual bool SendData(const std::string& data) {
        return SendData(reinterpret_cast<const uint8_t*>(data.c_str()), data.length());
    }
    
    // 回调设置
    virtual void SetDataCallback(BleDataCallback callback) { data_callback_ = callback; }
    virtual void SetConnectionCallback(BleConnectionCallback callback) { connection_callback_ = callback; }
    
    // 工具函数
    static std::string BdAddrToString(const esp_bd_addr_t& addr);
    static void StringToBdAddr(const std::string& addr_str, esp_bd_addr_t& addr);
    static std::string ConnectionStateToString(BleConnectionState state);
    
    // 单例访问
    static BleManager* GetInstance() { return instance_; }
    
protected:
    BleManager() = default;
    
    // 回调函数
    BleDataCallback data_callback_;
    BleConnectionCallback connection_callback_;
    
    // 连接状态
    BleConnectionState connection_state_ = BLE_DISCONNECTED;
    esp_bd_addr_t connected_device_;
    
    // 初始化状态
    bool IsInitializedInternal() const { return initialized_; }
    void SetInitialized(bool initialized) { initialized_ = initialized; }
    
    // 通用初始化函数
    bool InitializeBluetooth();
    bool InitializeBle();
    
    // 事件处理
    virtual void HandleGapEvent(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) = 0;
    virtual void HandleGattsEvent(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param) = 0;
    virtual void HandleGattcEvent(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_ble_gattc_cb_param_t* param) = 0;
    
    // 静态回调函数
    static void GapEventHandler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param);
    static void GattsEventHandler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param);
    static void GattcEventHandler(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_ble_gattc_cb_param_t* param);
    
private:
    static BleManager* instance_;
    bool initialized_ = false;
};

#endif // BLE_MANAGER_H
