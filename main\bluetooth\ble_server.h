#ifndef BLE_SERVER_H
#define BLE_SERVER_H

#include "ble_manager.h"
#include "esp_gatts_api.h"
#include "esp_gap_ble_api.h"
#include <map>

// GATT服务和特征UUID定义
#define GATTS_SERVICE_UUID_A        0x00FF
#define GATTS_CHAR_UUID_A           0xFF01
#define GATTS_DESCR_UUID_A          0x3333
#define GATTS_NUM_HANDLE_A          4

#define GATTS_DEMO_CHAR_VAL_LEN_MAX 0x40

// BLE服务器类 - 用于ESP32-S3作为BLE外设
class BleServer : public BleManager {
public:
    static BleServer* GetInstance();
    static void DestroyInstance();
    
    // 基本操作
    bool Initialize() override;
    bool Deinitialize() override;
    bool IsInitialized() const override { return IsInitializedInternal(); }
    
    // 服务器特定操作
    bool StartAdvertising();
    bool StopAdvertising();
    bool IsAdvertising() const { return advertising_; }
    
    // 连接管理
    BleConnectionState GetConnectionState() const override { return connection_state_; }
    
    // 数据传输
    bool SendData(const uint8_t* data, size_t length) override;
    
    // 配置
    void SetDeviceName(const std::string& name);
    std::string GetDeviceName() const { return device_name_; }
    
    // 客户端信息
    bool IsClientConnected() const { return connection_state_ == BLE_CONNECTED; }
    const esp_bd_addr_t& GetConnectedDevice() const { return connected_device_; }
    
protected:
    // 事件处理
    void HandleGapEvent(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) override;
    void HandleGattsEvent(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param) override;
    void HandleGattcEvent(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_ble_gattc_cb_param_t* param) override;
    
private:
    BleServer();
    ~BleServer();
    
    static BleServer* instance_;
    
    // 设备配置
    std::string device_name_;
    bool advertising_;
    
    // GATT相关
    esp_gatt_if_t gatts_if_;
    uint16_t service_handle_;
    uint16_t char_handle_;
    uint16_t descr_handle_;
    uint16_t conn_id_;
    
    // 内部方法
    bool SetupGattService();
    bool SetupAdvertising();
    void PrepareAdvertisingData();
    void PrepareAdvertisingParams();
    
    // GATT事件处理
    void HandleGattsRegisterEvent(esp_ble_gatts_cb_param_t* param);
    void HandleGattsCreateEvent(esp_ble_gatts_cb_param_t* param);
    void HandleGattsAddCharEvent(esp_ble_gatts_cb_param_t* param);
    void HandleGattsConnectEvent(esp_ble_gatts_cb_param_t* param);
    void HandleGattsDisconnectEvent(esp_ble_gatts_cb_param_t* param);
    void HandleGattsWriteEvent(esp_ble_gatts_cb_param_t* param);
    void HandleGattsReadEvent(esp_ble_gatts_cb_param_t* param);
    
    // GAP事件处理
    void HandleGapAdvDataSetEvent(esp_ble_gap_cb_param_t* param);
    void HandleGapAdvStartEvent(esp_ble_gap_cb_param_t* param);
    void HandleGapAdvStopEvent(esp_ble_gap_cb_param_t* param);
    
    // 广告数据
    esp_ble_adv_data_t adv_data_;
    esp_ble_adv_params_t adv_params_;
    
    // 特征值
    uint8_t char_value_[GATTS_DEMO_CHAR_VAL_LEN_MAX];
    esp_attr_value_t gatts_demo_char_val_;
    
    // 应用ID
    static constexpr uint16_t GATTS_APP_ID = 0x55;
};

#endif // BLE_SERVER_H
