# ESP32-S3 DevKitC-1 Bluetooth Client CMakeLists.txt

cmake_minimum_required(VERSION 3.16)

# 设置项目名称
set(PROJECT_NAME "esp32_devkitc_bt_client")

# 包含ESP-IDF构建系统
include($ENV{IDF_PATH}/tools/cmake/project.cmake)

# 定义项目
project(${PROJECT_NAME})

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加编译选项
target_compile_options(${PROJECT_NAME}.elf PRIVATE
    -Wall
    -Wextra
    -Wno-unused-parameter
    -Wno-missing-field-initializers
)

# 蓝牙配置
set(CONFIG_BT_ENABLED y)
set(CONFIG_BT_CLASSIC_ENABLED y)
set(CONFIG_BT_SPP_ENABLED y)
set(CONFIG_BT_CONTROLLER_ENABLED y)

# 内存配置
set(CONFIG_ESP_MAIN_TASK_STACK_SIZE 8192)
set(CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE 4096)

# 日志配置
set(CONFIG_LOG_DEFAULT_LEVEL_INFO y)
set(CONFIG_LOG_MAXIMUM_LEVEL 4)

# FreeRTOS配置
set(CONFIG_FREERTOS_HZ 1000)
set(CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER y)

# 编译器优化
set(CONFIG_COMPILER_OPTIMIZATION_SIZE y)

# 设置目标芯片
idf_build_set_property(TARGET esp32s3)
