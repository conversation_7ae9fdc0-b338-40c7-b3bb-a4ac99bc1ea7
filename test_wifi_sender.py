#!/usr/bin/env python3
"""
ESP32-S3 PICO-1 WiFi数据发送测试脚本

这个脚本用于向ESP32-S3 PICO-1发送测试数据，验证WiFi到蓝牙的桥接功能。
"""

import socket
import time
import json
import random
import threading
import argparse
from datetime import datetime

class WiFiDataSender:
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.running = False
        self.sent_count = 0
        self.error_count = 0
        
    def send_single_message(self, message):
        """发送单条消息"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)  # 5秒超时
            sock.connect((self.host, self.port))
            sock.send(message.encode('utf-8'))
            self.sent_count += 1
            print(f"✓ 发送成功: {message}")
            return True
        except Exception as e:
            self.error_count += 1
            print(f"✗ 发送失败: {e}")
            return False
        finally:
            try:
                sock.close()
            except:
                pass
    
    def send_sensor_data(self):
        """发送模拟传感器数据"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        temperature = round(random.uniform(20.0, 30.0), 1)
        humidity = round(random.uniform(40.0, 80.0), 1)
        pressure = round(random.uniform(1000.0, 1020.0), 1)
        
        sensor_data = {
            "timestamp": timestamp,
            "temperature": temperature,
            "humidity": humidity,
            "pressure": pressure,
            "device_id": "sensor_001"
        }
        
        message = json.dumps(sensor_data)
        return self.send_single_message(message)
    
    def send_text_message(self, text):
        """发送文本消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        message = f"[{timestamp}] {text}"
        return self.send_single_message(message)
    
    def send_continuous_data(self, interval=2.0):
        """连续发送数据"""
        self.running = True
        print(f"开始连续发送数据到 {self.host}:{self.port}，间隔 {interval} 秒")
        print("按 Ctrl+C 停止发送")
        
        try:
            while self.running:
                # 随机选择发送类型
                if random.choice([True, False]):
                    self.send_sensor_data()
                else:
                    messages = [
                        "系统状态正常",
                        "设备在线",
                        "数据采集中",
                        "温度报警",
                        "湿度异常",
                        "系统重启",
                        "网络连接恢复"
                    ]
                    self.send_text_message(random.choice(messages))
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n收到停止信号，正在停止...")
            self.running = False
    
    def send_test_batch(self, count=10):
        """发送测试批次数据"""
        print(f"发送 {count} 条测试消息到 {self.host}:{self.port}")
        
        for i in range(count):
            if i % 2 == 0:
                self.send_sensor_data()
            else:
                self.send_text_message(f"测试消息 #{i+1}")
            
            time.sleep(0.5)  # 短暂延时
        
        print(f"批次发送完成: 成功 {self.sent_count}, 失败 {self.error_count}")
    
    def get_statistics(self):
        """获取发送统计"""
        return {
            "sent_count": self.sent_count,
            "error_count": self.error_count,
            "success_rate": self.sent_count / (self.sent_count + self.error_count) * 100 if (self.sent_count + self.error_count) > 0 else 0
        }

def test_connection(host, port):
    """测试连接"""
    print(f"测试连接到 {host}:{port}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3.0)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print("✓ 连接测试成功")
            return True
        else:
            print("✗ 连接测试失败")
            return False
    except Exception as e:
        print(f"✗ 连接测试异常: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="ESP32-S3 PICO-1 WiFi数据发送测试工具")
    parser.add_argument("host", help="ESP32-S3 PICO-1的IP地址")
    parser.add_argument("-p", "--port", type=int, default=8080, help="端口号 (默认: 8080)")
    parser.add_argument("-m", "--mode", choices=["single", "batch", "continuous"], default="single",
                       help="发送模式: single(单条), batch(批次), continuous(连续)")
    parser.add_argument("-c", "--count", type=int, default=10, help="批次模式下的消息数量 (默认: 10)")
    parser.add_argument("-i", "--interval", type=float, default=2.0, help="连续模式下的发送间隔秒数 (默认: 2.0)")
    parser.add_argument("-t", "--text", help="单条模式下发送的文本消息")
    parser.add_argument("--test-connection", action="store_true", help="仅测试连接")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("ESP32-S3 PICO-1 WiFi数据发送测试工具")
    print("=" * 60)
    
    # 测试连接
    if not test_connection(args.host, args.port):
        print("无法连接到目标设备，请检查:")
        print("1. ESP32-S3 PICO-1是否已启动")
        print("2. WiFi连接是否正常")
        print("3. IP地址和端口是否正确")
        print("4. 防火墙设置")
        return
    
    if args.test_connection:
        return
    
    # 创建发送器
    sender = WiFiDataSender(args.host, args.port)
    
    try:
        if args.mode == "single":
            if args.text:
                sender.send_text_message(args.text)
            else:
                sender.send_sensor_data()
                
        elif args.mode == "batch":
            sender.send_test_batch(args.count)
            
        elif args.mode == "continuous":
            sender.send_continuous_data(args.interval)
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    
    # 显示统计信息
    stats = sender.get_statistics()
    print("\n" + "=" * 40)
    print("发送统计:")
    print(f"成功发送: {stats['sent_count']} 条")
    print(f"发送失败: {stats['error_count']} 条")
    print(f"成功率: {stats['success_rate']:.1f}%")
    print("=" * 40)

if __name__ == "__main__":
    main()
