#include "esp_log.h"
#include "nvs_flash.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "devkitc_bluetooth_client.h"
#include <string>

static const char* TAG = "DevKitC_Main";

// 全局变量
static DevKitCBluetoothClient* g_bt_client = nullptr;
static QueueHandle_t g_data_queue = nullptr;

// 数据队列结构体
struct DataPacket {
    uint8_t data[512];
    size_t length;
    char timestamp[32];
};

/**
 * @brief 数据接收回调函数
 */
static void data_received_callback(const uint8_t* data, size_t length) {
    ESP_LOGI(TAG, "Data received: %d bytes", length);
    
    // 打印接收到的数据（如果是文本）
    if (length > 0) {
        char* text_data = (char*)malloc(length + 1);
        if (text_data) {
            memcpy(text_data, data, length);
            text_data[length] = '\0';
            ESP_LOGI(TAG, "Received data: %s", text_data);
            free(text_data);
        }
    }
    
    // 将数据放入队列进行进一步处理
    if (g_data_queue && length <= sizeof(((DataPacket*)0)->data)) {
        DataPacket packet;
        memcpy(packet.data, data, length);
        packet.length = length;
        
        // 添加时间戳
        time_t now;
        time(&now);
        struct tm* timeinfo = localtime(&now);
        strftime(packet.timestamp, sizeof(packet.timestamp), "%Y-%m-%d %H:%M:%S", timeinfo);
        
        if (xQueueSend(g_data_queue, &packet, pdMS_TO_TICKS(100)) != pdTRUE) {
            ESP_LOGW(TAG, "Failed to queue data packet");
        }
    }
}

/**
 * @brief 连接状态回调函数
 */
static void connection_status_callback(bool connected, const std::string& device_name) {
    if (connected) {
        ESP_LOGI(TAG, "Connected to device: %s", device_name.c_str());
        ESP_LOGI(TAG, "Ready to receive data from WiFi-Bluetooth bridge");
    } else {
        ESP_LOGI(TAG, "Disconnected from device: %s", device_name.c_str());
        ESP_LOGI(TAG, "Attempting to reconnect...");
    }
}

/**
 * @brief 状态消息回调函数
 */
static void status_callback(const std::string& message) {
    ESP_LOGI(TAG, "Status: %s", message.c_str());
}

/**
 * @brief 数据处理任务
 */
static void data_processing_task(void* pvParameters) {
    DataPacket packet;
    
    ESP_LOGI(TAG, "Data processing task started");
    
    while (true) {
        if (xQueueReceive(g_data_queue, &packet, portMAX_DELAY) == pdTRUE) {
            ESP_LOGI(TAG, "Processing data packet:");
            ESP_LOGI(TAG, "  Timestamp: %s", packet.timestamp);
            ESP_LOGI(TAG, "  Length: %d bytes", packet.length);
            
            // 这里可以添加你的数据处理逻辑
            // 例如：
            // - 解析数据格式
            // - 存储到文件系统
            // - 发送到其他设备
            // - 控制LED、电机等硬件
            // - 显示在屏幕上
            
            // 示例：简单的数据分析
            if (packet.length > 0) {
                // 检查是否包含特定关键字
                char* data_str = (char*)malloc(packet.length + 1);
                if (data_str) {
                    memcpy(data_str, packet.data, packet.length);
                    data_str[packet.length] = '\0';
                    
                    if (strstr(data_str, "temperature") != nullptr) {
                        ESP_LOGI(TAG, "Temperature data detected");
                    } else if (strstr(data_str, "humidity") != nullptr) {
                        ESP_LOGI(TAG, "Humidity data detected");
                    } else if (strstr(data_str, "sensor") != nullptr) {
                        ESP_LOGI(TAG, "Sensor data detected");
                    }
                    
                    free(data_str);
                }
            }
            
            // 打印原始数据（十六进制）
            ESP_LOG_BUFFER_HEX(TAG, packet.data, packet.length);
        }
    }
}

/**
 * @brief 状态监控任务
 */
static void status_monitor_task(void* pvParameters) {
    uint32_t last_packets = 0;
    uint32_t last_bytes = 0;
    uint32_t last_attempts = 0;
    
    while (true) {
        if (g_bt_client) {
            uint32_t packets, bytes, attempts;
            g_bt_client->GetStatistics(packets, bytes, attempts);
            
            // 检查是否有新的统计数据
            if (packets != last_packets || bytes != last_bytes || attempts != last_attempts) {
                ESP_LOGI(TAG, "Statistics - Packets: %lu, Bytes: %lu, Connection attempts: %lu",
                         packets, bytes, attempts);
                
                last_packets = packets;
                last_bytes = bytes;
                last_attempts = attempts;
            }
            
            // 显示连接状态
            ESP_LOGI(TAG, "Connection status: %s", 
                     g_bt_client->IsConnected() ? "Connected" : "Disconnected");
            
            // 显示发现的设备数量
            ESP_LOGI(TAG, "Discovered devices: %d", g_bt_client->GetDiscoveredDevices().size());
        }
        
        vTaskDelay(pdMS_TO_TICKS(15000)); // 每15秒检查一次
    }
}

/**
 * @brief 用户交互任务（可选）
 */
static void user_interaction_task(void* pvParameters) {
    while (true) {
        // 这里可以添加用户交互逻辑
        // 例如：
        // - 按键检测
        // - LED状态指示
        // - 串口命令处理
        // - 显示屏更新
        
        // 示例：每30秒发送一个心跳消息
        if (g_bt_client && g_bt_client->IsConnected()) {
            std::string heartbeat = "DevKitC-1 Heartbeat: " + std::to_string(esp_timer_get_time() / 1000);
            g_bt_client->SendString(heartbeat);
            ESP_LOGD(TAG, "Sent heartbeat message");
        }
        
        vTaskDelay(pdMS_TO_TICKS(30000)); // 每30秒
    }
}

/**
 * @brief 主函数
 */
extern "C" void app_main(void) {
    ESP_LOGI(TAG, "ESP32-S3 DevKitC-1 Bluetooth Client Starting...");
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 创建数据队列
    g_data_queue = xQueueCreate(10, sizeof(DataPacket));
    if (!g_data_queue) {
        ESP_LOGE(TAG, "Failed to create data queue");
        return;
    }
    
    // 创建蓝牙客户端
    ESP_LOGI(TAG, "Creating Bluetooth client...");
    g_bt_client = new DevKitCBluetoothClient("ESP32-S3-PICO-Bridge");
    
    // 设置回调函数
    g_bt_client->SetDataReceivedCallback(data_received_callback);
    g_bt_client->SetConnectionStatusCallback(connection_status_callback);
    g_bt_client->SetStatusCallback(status_callback);
    
    // 启用自动重连
    g_bt_client->SetAutoReconnect(true);
    
    // 初始化蓝牙客户端
    if (!g_bt_client->Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth client");
        delete g_bt_client;
        return;
    }
    
    ESP_LOGI(TAG, "Bluetooth client initialized successfully");
    ESP_LOGI(TAG, "Target device: ESP32-S3-PICO-Bridge");
    ESP_LOGI(TAG, "Starting device discovery...");
    
    // 开始发现设备
    if (!g_bt_client->StartDiscovery(30)) {
        ESP_LOGE(TAG, "Failed to start device discovery");
        delete g_bt_client;
        return;
    }
    
    // 创建任务
    xTaskCreate(data_processing_task, "data_processing", 4096, NULL, 5, NULL);
    xTaskCreate(status_monitor_task, "status_monitor", 3072, NULL, 3, NULL);
    xTaskCreate(user_interaction_task, "user_interaction", 2048, NULL, 2, NULL);
    
    ESP_LOGI(TAG, "All tasks created successfully");
    ESP_LOGI(TAG, "DevKitC-1 is ready to receive data from PICO-1");
    ESP_LOGI(TAG, "Waiting for connection to ESP32-S3-PICO-Bridge...");
    
    // 主循环
    while (true) {
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 这里可以添加其他周期性任务
        // 例如：看门狗喂狗、系统状态检查等
    }
    
    // 清理资源（通常不会执行到这里）
    if (g_bt_client) {
        g_bt_client->Disconnect();
        delete g_bt_client;
        g_bt_client = nullptr;
    }
    
    if (g_data_queue) {
        vQueueDelete(g_data_queue);
        g_data_queue = nullptr;
    }
}
