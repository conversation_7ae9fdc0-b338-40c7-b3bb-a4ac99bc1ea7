#ifndef BLE_CLIENT_H
#define BLE_CLIENT_H

#include "ble_manager.h"
#include "esp_gattc_api.h"
#include "esp_gap_ble_api.h"
#include <vector>

// BLE客户端类 - 用于ESP32-S3作为BLE中心设备
class BleClient : public BleManager {
public:
    static BleClient* GetInstance();
    static void DestroyInstance();
    
    // 基本操作
    bool Initialize() override;
    bool Deinitialize() override;
    bool IsInitialized() const override { return IsInitializedInternal(); }
    
    // 扫描操作
    bool StartScan(uint32_t duration = 30);
    bool StopScan();
    bool IsScanning() const { return scanning_; }
    
    // 设备发现
    std::vector<BleDeviceInfo> GetDiscoveredDevices() const { return discovered_devices_; }
    void ClearDiscoveredDevices() { discovered_devices_.clear(); }
    
    // 连接管理
    bool Connect(const esp_bd_addr_t& address);
    bool Disconnect();
    BleConnectionState GetConnectionState() const override { return connection_state_; }
    
    // 数据传输
    bool SendData(const uint8_t* data, size_t length) override;
    
    // 配置
    void SetDeviceName(const std::string& name);
    std::string GetDeviceName() const { return device_name_; }
    
    // 目标设备信息
    const esp_bd_addr_t& GetConnectedDevice() const { return connected_device_; }
    
protected:
    // 事件处理
    void HandleGapEvent(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) override;
    void HandleGattsEvent(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param) override;
    void HandleGattcEvent(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_ble_gattc_cb_param_t* param) override;
    
private:
    BleClient();
    ~BleClient();
    
    static BleClient* instance_;
    
    // 设备配置
    std::string device_name_;
    bool scanning_;
    
    // 发现的设备
    std::vector<BleDeviceInfo> discovered_devices_;
    
    // GATT客户端相关
    esp_gatt_if_t gattc_if_;
    uint16_t conn_id_;
    uint16_t service_start_handle_;
    uint16_t service_end_handle_;
    uint16_t char_handle_;
    
    // 扫描参数
    esp_ble_scan_params_t scan_params_;
    
    // 内部方法
    bool SetupScanParams();
    void AddDiscoveredDevice(const esp_ble_gap_cb_param_t* param);
    bool FindTargetService();
    bool FindTargetCharacteristic();
    
    // GAP事件处理
    void HandleGapScanResultEvent(esp_ble_gap_cb_param_t* param);
    void HandleGapScanStartEvent(esp_ble_gap_cb_param_t* param);
    void HandleGapScanStopEvent(esp_ble_gap_cb_param_t* param);
    
    // GATTC事件处理
    void HandleGattcRegisterEvent(esp_ble_gattc_cb_param_t* param);
    void HandleGattcOpenEvent(esp_ble_gattc_cb_param_t* param);
    void HandleGattcCloseEvent(esp_ble_gattc_cb_param_t* param);
    void HandleGattcSearchResultEvent(esp_ble_gattc_cb_param_t* param);
    void HandleGattcSearchCompleteEvent(esp_ble_gattc_cb_param_t* param);
    void HandleGattcGetCharEvent(esp_ble_gattc_cb_param_t* param);
    void HandleGattcNotifyEvent(esp_ble_gattc_cb_param_t* param);
    void HandleGattcWriteEvent(esp_ble_gattc_cb_param_t* param);
    
    // 应用ID
    static constexpr uint16_t GATTC_APP_ID = 0x56;
    
    // 目标服务和特征UUID
    static constexpr uint16_t TARGET_SERVICE_UUID = 0x00FF;
    static constexpr uint16_t TARGET_CHAR_UUID = 0xFF01;
};

#endif // BLE_CLIENT_H
