#ifndef BLUETOOTH_CLIENT_H
#define BLUETOOTH_CLIENT_H

#include "bluetooth_manager.h"
#include "esp_gap_bt_api.h"
#include "esp_spp_api.h"
#include <vector>

// 蓝牙客户端类 - 用于ESP32-S3-DevKitC-1
class BluetoothClient : public BluetoothManager {
private:
    static const char* CLIENT_NAME;
    static const uint8_t DISCOVERY_DURATION;
    static const esp_spp_sec_t SPP_SEC_MASK;
    static const esp_spp_role_t SPP_ROLE;
    
    bool discovery_active_;
    std::vector<BluetoothDeviceInfo> discovered_devices_;
    
    // 目标服务器信息
    std::string target_server_name_;
    esp_bd_addr_t target_server_address_;
    bool has_target_address_;

public:
    BluetoothClient(const std::string& device_name = "ESP32-S3-DevKitC-Client");
    virtual ~BluetoothClient();
    
    // 重写基类方法
    virtual bool Initialize() override;
    virtual bool StartDiscovery() override;
    virtual bool StopDiscovery() override;
    virtual bool Connect(const esp_bd_addr_t& address) override;
    
    // 客户端特有方法
    bool ConnectToServer(const std::string& server_name);
    bool ConnectToAddress(const std::string& address_str);
    std::vector<BluetoothDeviceInfo> GetDiscoveredDevices() const;
    void ClearDiscoveredDevices();
    
    // 配置方法
    void SetTargetServerName(const std::string& server_name);
    void SetTargetServerAddress(const esp_bd_addr_t& address);
    void SetTargetServerAddress(const std::string& address_str);
    
    // 状态查询
    bool IsDiscoveryActive() const { return discovery_active_; }
    size_t GetDiscoveredDeviceCount() const { return discovered_devices_.size(); }
    std::string GetTargetServerName() const { return target_server_name_; }

protected:
    // 内部回调处理
    void HandleGapEvent(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param);
    void HandleSppEvent(esp_spp_cb_event_t event, esp_spp_cb_param_t* param);
    
private:
    // 初始化相关
    bool InitializeGap();
    bool InitializeSpp();
    bool SetDeviceProperties();
    
    // 设备发现相关
    void AddDiscoveredDevice(const BluetoothDeviceInfo& device);
    bool IsDeviceAlreadyDiscovered(const esp_bd_addr_t& address) const;
    BluetoothDeviceInfo* FindDiscoveredDevice(const esp_bd_addr_t& address);
    BluetoothDeviceInfo* FindDiscoveredDevice(const std::string& name);
    
    // 静态回调函数
    static void StaticGapCallback(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param);
    static void StaticSppCallback(esp_spp_cb_event_t event, esp_spp_cb_param_t* param);
    
    static BluetoothClient* instance_;
};

#endif // BLUETOOTH_CLIENT_H
