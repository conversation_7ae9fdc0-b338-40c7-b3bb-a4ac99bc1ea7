#include "ble_client.h"
#include <cstring>
#include <algorithm>

static const char* TAG = "BleClient";

BleClient* BleClient::instance_ = nullptr;

BleClient::BleClient() 
    : device_name_("ESP32-S3-BLE-Client")
    , scanning_(false)
    , gattc_if_(ESP_GATT_IF_NONE)
    , conn_id_(0)
    , service_start_handle_(0)
    , service_end_handle_(0)
    , char_handle_(0) {
    
    // 设置实例
    instance_ = this;
    BleManager::instance_ = this;
}

BleClient::~BleClient() {
    Deinitialize();
    instance_ = nullptr;
    BleManager::instance_ = nullptr;
}

BleClient* BleClient::GetInstance() {
    if (!instance_) {
        instance_ = new BleClient();
    }
    return instance_;
}

void BleClient::DestroyInstance() {
    if (instance_) {
        delete instance_;
        instance_ = nullptr;
    }
}

bool BleClient::Initialize() {
    if (IsInitializedInternal()) {
        ESP_LOGW(TAG, "BLE client already initialized");
        return true;
    }
    
    ESP_LOGI(TAG, "Initializing BLE client");
    
    // 初始化蓝牙控制器
    if (!InitializeBluetooth()) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth");
        return false;
    }
    
    // 初始化BLE
    if (!InitializeBle()) {
        ESP_LOGE(TAG, "Failed to initialize BLE");
        return false;
    }
    
    // 设置扫描参数
    if (!SetupScanParams()) {
        ESP_LOGE(TAG, "Failed to setup scan parameters");
        return false;
    }
    
    // 注册GATT客户端应用
    esp_err_t ret = esp_ble_gattc_app_register(GATTC_APP_ID);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GATT client app: %s", esp_err_to_name(ret));
        return false;
    }
    
    SetInitialized(true);
    ESP_LOGI(TAG, "BLE client initialized successfully");
    return true;
}

bool BleClient::Deinitialize() {
    if (!IsInitializedInternal()) {
        return true;
    }
    
    ESP_LOGI(TAG, "Deinitializing BLE client");
    
    StopScan();
    Disconnect();
    
    if (gattc_if_ != ESP_GATT_IF_NONE) {
        esp_ble_gattc_app_unregister(gattc_if_);
    }
    
    esp_bluedroid_disable();
    esp_bluedroid_deinit();
    esp_bt_controller_disable();
    esp_bt_controller_deinit();
    
    SetInitialized(false);
    ESP_LOGI(TAG, "BLE client deinitialized");
    return true;
}

bool BleClient::StartScan(uint32_t duration) {
    if (!IsInitializedInternal()) {
        ESP_LOGE(TAG, "BLE client not initialized");
        return false;
    }
    
    if (scanning_) {
        ESP_LOGW(TAG, "Already scanning");
        return true;
    }
    
    ESP_LOGI(TAG, "Starting BLE scan for %lu seconds", duration);
    
    // 清除之前发现的设备
    discovered_devices_.clear();
    
    esp_err_t ret = esp_ble_gap_start_scanning(duration);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start scanning: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool BleClient::StopScan() {
    if (!scanning_) {
        return true;
    }
    
    ESP_LOGI(TAG, "Stopping BLE scan");
    
    esp_err_t ret = esp_ble_gap_stop_scanning();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop scanning: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool BleClient::Connect(const esp_bd_addr_t& address) {
    if (!IsInitializedInternal()) {
        ESP_LOGE(TAG, "BLE client not initialized");
        return false;
    }
    
    if (connection_state_ == BLE_CONNECTED || connection_state_ == BLE_CONNECTING) {
        ESP_LOGW(TAG, "Already connected or connecting");
        return false;
    }
    
    ESP_LOGI(TAG, "Connecting to device: %s", BdAddrToString(address).c_str());
    
    connection_state_ = BLE_CONNECTING;
    memcpy(connected_device_, address, sizeof(esp_bd_addr_t));
    
    esp_err_t ret = esp_ble_gattc_open(gattc_if_, const_cast<esp_bd_addr_t&>(address), BLE_ADDR_TYPE_PUBLIC, true);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to connect: %s", esp_err_to_name(ret));
        connection_state_ = BLE_DISCONNECTED;
        return false;
    }
    
    return true;
}

bool BleClient::Disconnect() {
    if (connection_state_ == BLE_DISCONNECTED) {
        return true;
    }
    
    ESP_LOGI(TAG, "Disconnecting from device");
    
    connection_state_ = BLE_DISCONNECTING;
    
    esp_err_t ret = esp_ble_gattc_close(gattc_if_, conn_id_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to disconnect: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool BleClient::SendData(const uint8_t* data, size_t length) {
    if (connection_state_ != BLE_CONNECTED) {
        ESP_LOGE(TAG, "Not connected to any device");
        return false;
    }
    
    if (char_handle_ == 0) {
        ESP_LOGE(TAG, "Characteristic handle not found");
        return false;
    }
    
    ESP_LOGI(TAG, "Sending data: %zu bytes", length);
    
    esp_err_t ret = esp_ble_gattc_write_char(gattc_if_, conn_id_, char_handle_, 
                                             length, const_cast<uint8_t*>(data), 
                                             ESP_GATT_WRITE_TYPE_RSP, ESP_GATT_AUTH_REQ_NONE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to write characteristic: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

void BleClient::SetDeviceName(const std::string& name) {
    device_name_ = name;
    if (IsInitializedInternal()) {
        esp_ble_gap_set_device_name(device_name_.c_str());
    }
}

bool BleClient::SetupScanParams() {
    scan_params_.scan_type = BLE_SCAN_TYPE_ACTIVE;
    scan_params_.own_addr_type = BLE_ADDR_TYPE_PUBLIC;
    scan_params_.scan_filter_policy = BLE_SCAN_FILTER_ALLOW_ALL;
    scan_params_.scan_interval = 0x50;
    scan_params_.scan_window = 0x30;
    scan_params_.scan_duplicate = BLE_SCAN_DUPLICATE_DISABLE;
    
    esp_err_t ret = esp_ble_gap_set_scan_params(&scan_params_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set scan parameters: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

void BleClient::HandleGapEvent(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) {
    switch (event) {
        case ESP_GAP_BLE_SCAN_RESULT_EVT:
            HandleGapScanResultEvent(param);
            break;
        case ESP_GAP_BLE_SCAN_START_COMPLETE_EVT:
            HandleGapScanStartEvent(param);
            break;
        case ESP_GAP_BLE_SCAN_STOP_COMPLETE_EVT:
            HandleGapScanStopEvent(param);
            break;
        default:
            ESP_LOGD(TAG, "GAP event: %d", event);
            break;
    }
}

void BleClient::HandleGattsEvent(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param) {
    // BLE客户端通常不需要处理GATTS事件
    ESP_LOGD(TAG, "GATTS event: %d", event);
}

void BleClient::HandleGattcEvent(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_ble_gattc_cb_param_t* param) {
    if (event == ESP_GATTC_REG_EVT) {
        if (param->reg.status == ESP_GATT_OK) {
            gattc_if_ = gattc_if;
        }
        HandleGattcRegisterEvent(param);
        return;
    }

    if (gattc_if == ESP_GATT_IF_NONE || gattc_if == gattc_if_) {
        switch (event) {
            case ESP_GATTC_OPEN_EVT:
                HandleGattcOpenEvent(param);
                break;
            case ESP_GATTC_CLOSE_EVT:
                HandleGattcCloseEvent(param);
                break;
            case ESP_GATTC_SEARCH_RES_EVT:
                HandleGattcSearchResultEvent(param);
                break;
            case ESP_GATTC_SEARCH_CMPL_EVT:
                HandleGattcSearchCompleteEvent(param);
                break;
            case ESP_GATTC_GET_CHAR_EVT:
                HandleGattcGetCharEvent(param);
                break;
            case ESP_GATTC_NOTIFY_EVT:
                HandleGattcNotifyEvent(param);
                break;
            case ESP_GATTC_WRITE_EVT:
                HandleGattcWriteEvent(param);
                break;
            default:
                ESP_LOGD(TAG, "GATTC event: %d", event);
                break;
        }
    }
}

void BleClient::AddDiscoveredDevice(const esp_ble_gap_cb_param_t* param) {
    const esp_ble_gap_cb_param_t::ble_scan_result_evt_param& scan_result = param->scan_rst;

    // 检查是否已经发现过这个设备
    for (const auto& device : discovered_devices_) {
        if (memcmp(device.address, scan_result.bda, sizeof(esp_bd_addr_t)) == 0) {
            return; // 设备已存在
        }
    }

    // 添加新设备
    BleDeviceInfo device;
    memcpy(device.address, scan_result.bda, sizeof(esp_bd_addr_t));
    device.rssi = scan_result.rssi;
    device.adv_data_len = std::min(scan_result.adv_data_len, (uint16_t)ESP_BLE_ADV_DATA_LEN_MAX);
    device.scan_rsp_len = std::min(scan_result.scan_rsp_len, (uint16_t)ESP_BLE_SCAN_RSP_DATA_LEN_MAX);

    if (device.adv_data_len > 0) {
        memcpy(device.adv_data, scan_result.ble_adv, device.adv_data_len);
    }
    if (device.scan_rsp_len > 0) {
        memcpy(device.scan_rsp_data, scan_result.ble_adv + device.adv_data_len, device.scan_rsp_len);
    }

    // 尝试解析设备名称
    device.name = "Unknown";
    // 这里可以添加解析广告数据中设备名称的代码

    discovered_devices_.push_back(device);
    ESP_LOGI(TAG, "Discovered device: %s, RSSI: %d", BdAddrToString(device.address).c_str(), device.rssi);
}

void BleClient::HandleGapScanResultEvent(esp_ble_gap_cb_param_t* param) {
    esp_ble_gap_cb_param_t::ble_scan_result_evt_param& scan_result = param->scan_rst;

    switch (scan_result.search_evt) {
        case ESP_GAP_SEARCH_INQ_RES_EVT:
            AddDiscoveredDevice(param);
            break;
        case ESP_GAP_SEARCH_INQ_CMPL_EVT:
            ESP_LOGI(TAG, "Scan complete");
            scanning_ = false;
            break;
        default:
            break;
    }
}

void BleClient::HandleGapScanStartEvent(esp_ble_gap_cb_param_t* param) {
    ESP_LOGI(TAG, "Scan start complete, status: %d", param->scan_start_cmpl.status);

    if (param->scan_start_cmpl.status == ESP_BT_STATUS_SUCCESS) {
        scanning_ = true;
        ESP_LOGI(TAG, "Scan started successfully");
    } else {
        ESP_LOGE(TAG, "Failed to start scan");
    }
}

void BleClient::HandleGapScanStopEvent(esp_ble_gap_cb_param_t* param) {
    ESP_LOGI(TAG, "Scan stop complete, status: %d", param->scan_stop_cmpl.status);

    scanning_ = false;
    ESP_LOGI(TAG, "Scan stopped");
}
