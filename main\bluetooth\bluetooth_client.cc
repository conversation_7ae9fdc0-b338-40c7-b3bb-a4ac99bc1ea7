#include "bluetooth_client.h"
#include "esp_log.h"
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_bt_device.h"
#include <cstring>
#include <algorithm>

const char* BluetoothClient::CLIENT_NAME = "ESP32-S3-DevKitC-Client";
const uint8_t BluetoothClient::DISCOVERY_DURATION = 10; // 10 * 1.28 seconds = 12.8 seconds
const esp_spp_sec_t BluetoothClient::SPP_SEC_MASK = ESP_SPP_SEC_AUTHENTICATE;
const esp_spp_role_t BluetoothClient::SPP_ROLE = ESP_SPP_ROLE_MASTER;

BluetoothClient* BluetoothClient::instance_ = nullptr;

BluetoothClient::BluetoothClient(const std::string& device_name)
    : BluetoothManager()
    , discovery_active_(false)
    , target_server_name_("")
    , has_target_address_(false) {
    instance_ = this;
    memset(target_server_address_, 0, sizeof(target_server_address_));
}

BluetoothClient::~BluetoothClient() {
    StopDiscovery();
    Disconnect();
    Deinitialize();
    instance_ = nullptr;
}

bool BluetoothClient::Initialize() {
    ESP_LOGI(TAG, "Initializing Bluetooth Client");
    
    if (IsInitialized()) {
        ESP_LOGW(TAG, "Bluetooth Client already initialized");
        return true;
    }
    
    // 初始化蓝牙控制器
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    esp_err_t ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bt_controller_enable(ESP_BT_MODE_CLASSIC_BT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 初始化蓝牙栈
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 初始化GAP和SPP
    if (!InitializeGap() || !InitializeSpp()) {
        ESP_LOGE(TAG, "Failed to initialize GAP or SPP");
        return false;
    }
    
    // 设置设备属性
    if (!SetDeviceProperties()) {
        ESP_LOGE(TAG, "Failed to set device properties");
        return false;
    }
    
    SetInitialized(true);
    ESP_LOGI(TAG, "Bluetooth Client initialized successfully");
    return true;
}

bool BluetoothClient::InitializeGap() {
    esp_err_t ret = esp_bt_gap_register_callback(StaticGapCallback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GAP callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 设置扫描模式为不可发现、不可连接（客户端模式）
    ret = esp_bt_gap_set_scan_mode(ESP_BT_NON_CONNECTABLE, ESP_BT_NON_DISCOVERABLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set scan mode: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool BluetoothClient::InitializeSpp() {
    esp_err_t ret = esp_spp_register_callback(StaticSppCallback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register SPP callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_spp_init(ESP_SPP_MODE_CB);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize SPP: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool BluetoothClient::SetDeviceProperties() {
    // 设置设备名称
    esp_err_t ret = esp_bt_dev_set_device_name(CLIENT_NAME);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set device name: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool BluetoothClient::StartDiscovery() {
    if (!IsInitialized()) {
        ESP_LOGE(TAG, "Bluetooth not initialized");
        return false;
    }
    
    if (discovery_active_) {
        ESP_LOGW(TAG, "Discovery already active");
        return true;
    }
    
    ESP_LOGI(TAG, "Starting device discovery");
    
    // 清除之前发现的设备
    discovered_devices_.clear();
    
    esp_err_t ret = esp_bt_gap_start_discovery(ESP_BT_INQ_MODE_GENERAL_INQUIRY, 
                                               DISCOVERY_DURATION, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start discovery: %s", esp_err_to_name(ret));
        return false;
    }
    
    discovery_active_ = true;
    ESP_LOGI(TAG, "Device discovery started");
    return true;
}

bool BluetoothClient::StopDiscovery() {
    if (!discovery_active_) {
        return true;
    }
    
    ESP_LOGI(TAG, "Stopping device discovery");
    
    esp_err_t ret = esp_bt_gap_cancel_discovery();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop discovery: %s", esp_err_to_name(ret));
        return false;
    }
    
    discovery_active_ = false;
    ESP_LOGI(TAG, "Device discovery stopped");
    return true;
}

bool BluetoothClient::Connect(const esp_bd_addr_t& address) {
    if (!IsInitialized()) {
        ESP_LOGE(TAG, "Bluetooth not initialized");
        return false;
    }
    
    if (connection_state_ == BT_CONNECTED) {
        ESP_LOGW(TAG, "Already connected to a device");
        return false;
    }
    
    if (connection_state_ == BT_CONNECTING) {
        ESP_LOGW(TAG, "Connection already in progress");
        return false;
    }
    
    ESP_LOGI(TAG, "Connecting to device: %02x:%02x:%02x:%02x:%02x:%02x",
             address[0], address[1], address[2], address[3], address[4], address[5]);
    
    // 停止发现（如果正在进行）
    if (discovery_active_) {
        StopDiscovery();
    }
    
    connection_state_ = BT_CONNECTING;
    memcpy(connected_device_address_, address, sizeof(esp_bd_addr_t));
    
    esp_bd_addr_t addr_copy;
    memcpy(addr_copy, address, sizeof(esp_bd_addr_t));
    esp_err_t ret = esp_spp_connect(SPP_SEC_MASK, SPP_ROLE, 0, addr_copy);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to connect: %s", esp_err_to_name(ret));
        connection_state_ = BT_DISCONNECTED;
        return false;
    }
    
    OnConnectionStateChanged(BT_CONNECTING, "");
    return true;
}

bool BluetoothClient::ConnectToServer(const std::string& server_name) {
    target_server_name_ = server_name;
    
    // 查找已发现的设备
    BluetoothDeviceInfo* device = FindDiscoveredDevice(server_name);
    if (device) {
        ESP_LOGI(TAG, "Found target server in discovered devices");
        return Connect(device->address);
    }
    
    // 如果没有找到，开始发现
    ESP_LOGI(TAG, "Target server not found, starting discovery");
    return StartDiscovery();
}

bool BluetoothClient::ConnectToAddress(const std::string& address_str) {
    esp_bd_addr_t address;
    StringToBdAddr(address_str, address);
    return Connect(address);
}

std::vector<BluetoothDeviceInfo> BluetoothClient::GetDiscoveredDevices() const {
    return discovered_devices_;
}

void BluetoothClient::ClearDiscoveredDevices() {
    discovered_devices_.clear();
}

void BluetoothClient::SetTargetServerName(const std::string& server_name) {
    target_server_name_ = server_name;
}

void BluetoothClient::SetTargetServerAddress(const esp_bd_addr_t& address) {
    memcpy(target_server_address_, address, sizeof(esp_bd_addr_t));
    has_target_address_ = true;
}

void BluetoothClient::SetTargetServerAddress(const std::string& address_str) {
    StringToBdAddr(address_str, target_server_address_);
    has_target_address_ = true;
}

void BluetoothClient::AddDiscoveredDevice(const BluetoothDeviceInfo& device) {
    // 检查设备是否已经存在
    if (!IsDeviceAlreadyDiscovered(device.address)) {
        discovered_devices_.push_back(device);
        ESP_LOGI(TAG, "Discovered device: %s (%s), RSSI: %d", 
                 device.name.c_str(), BdAddrToString(device.address).c_str(), device.rssi);
        
        // 通知上层应用
        OnDeviceDiscovered(device);
        
        // 如果这是目标服务器，自动连接
        if (!target_server_name_.empty() && device.name == target_server_name_) {
            ESP_LOGI(TAG, "Found target server: %s", target_server_name_.c_str());
            StopDiscovery();
            Connect(device.address);
        }
    }
}

bool BluetoothClient::IsDeviceAlreadyDiscovered(const esp_bd_addr_t& address) const {
    return std::any_of(discovered_devices_.begin(), discovered_devices_.end(),
                       [&address](const BluetoothDeviceInfo& device) {
                           return memcmp(device.address, address, sizeof(esp_bd_addr_t)) == 0;
                       });
}

BluetoothDeviceInfo* BluetoothClient::FindDiscoveredDevice(const esp_bd_addr_t& address) {
    auto it = std::find_if(discovered_devices_.begin(), discovered_devices_.end(),
                           [&address](const BluetoothDeviceInfo& device) {
                               return memcmp(device.address, address, sizeof(esp_bd_addr_t)) == 0;
                           });
    return (it != discovered_devices_.end()) ? &(*it) : nullptr;
}

BluetoothDeviceInfo* BluetoothClient::FindDiscoveredDevice(const std::string& name) {
    auto it = std::find_if(discovered_devices_.begin(), discovered_devices_.end(),
                           [&name](const BluetoothDeviceInfo& device) {
                               return device.name == name;
                           });
    return (it != discovered_devices_.end()) ? &(*it) : nullptr;
}

void BluetoothClient::StaticGapCallback(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGapEvent(event, param);
    }
}

void BluetoothClient::StaticSppCallback(esp_spp_cb_event_t event, esp_spp_cb_param_t* param) {
    if (instance_) {
        instance_->HandleSppEvent(event, param);
    }
}
