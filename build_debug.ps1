# Debug Build Script
Write-Host "Setting up ESP-IDF 5.4.2 environment..." -ForegroundColor Green

# Get current project directory
$PROJECT_DIR = Get-Location
Write-Host "Project directory: $PROJECT_DIR" -ForegroundColor Cyan

# Set ESP-IDF path to version 5.4.2
$env:IDF_PATH = "C:\Users\<USER>\esp\v5.4.2\esp-idf"

# Add local Git tools to PATH
$LOCAL_GIT_PATH = "$PROJECT_DIR\tools\idf-git\2.39.2\cmd"
$LOCAL_PYTHON_PATH = "$PROJECT_DIR\tools\idf-python\3.11.2"

# Add Python environment and tools to PATH, including local Git
$env:PATH = "$LOCAL_GIT_PATH;$LOCAL_PYTHON_PATH;D:\esp32-idf-ahy\5.3.2\python_env\idf5.3_py3.11_env\Scripts;D:\esp32-idf-ahy\5.3.2\tools;" + $env:PATH

Write-Host "Environment setup complete!" -ForegroundColor Yellow
Write-Host "IDF_PATH=$env:IDF_PATH" -ForegroundColor Cyan

# Build the project with verbose output
Write-Host "Starting build with verbose output..." -ForegroundColor Green
try {
    & "D:\esp32-idf-ahy\5.3.2\python_env\idf5.3_py3.11_env\Scripts\python.exe" "$env:IDF_PATH\tools\idf.py" build --verbose
    Write-Host "Build completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Build failed: $_" -ForegroundColor Red
    exit 1
}
