# WiFi Receiver Build Script
Write-Host "========================================" -ForegroundColor Green
Write-Host "Building ESP32S3 WiFi Data Receiver" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Get current project directory
$PROJECT_DIR = Get-Location
Write-Host "Project directory: $PROJECT_DIR" -ForegroundColor Cyan

# Set ESP-IDF path to version 5.4.2
$env:IDF_PATH = "C:\Users\<USER>\esp\v5.4.2\esp-idf"

# Add local Git tools to PATH
$LOCAL_GIT_PATH = "$PROJECT_DIR\tools\idf-git\2.39.2\cmd"
$LOCAL_PYTHON_PATH = "$PROJECT_DIR\tools\idf-python\3.11.2"

# Add Python environment and tools to PATH, including local Git
$env:PATH = "$LOCAL_GIT_PATH;$LOCAL_PYTHON_PATH;D:\esp32-idf-ahy\5.3.2\python_env\idf5.3_py3.11_env\Scripts;D:\esp32-idf-ahy\5.3.2\tools;" + $env:PATH

Write-Host "Environment setup complete!" -ForegroundColor Yellow
Write-Host "IDF_PATH=$env:IDF_PATH" -ForegroundColor Cyan

# Check if components exist
Write-Host "Checking WiFi data receiver component..." -ForegroundColor Yellow
if (Test-Path "components\wifi_data_receiver\CMakeLists.txt") {
    Write-Host "✓ WiFi data receiver component found" -ForegroundColor Green
} else {
    Write-Host "✗ WiFi data receiver component not found" -ForegroundColor Red
    exit 1
}

# Set target to ESP32S3
Write-Host "Setting target to ESP32S3..." -ForegroundColor Yellow
try {
    $result = & "D:\esp32-idf-ahy\5.3.2\python_env\idf5.3_py3.11_env\Scripts\python.exe" "$env:IDF_PATH\tools\idf.py" set-target esp32s3 2>&1
    Write-Host $result
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to set target" -ForegroundColor Red
        exit 1
    }
    Write-Host "✓ Target set to ESP32S3" -ForegroundColor Green
} catch {
    Write-Host "Error setting target: $_" -ForegroundColor Red
    exit 1
}

# Build the project
Write-Host "Building project..." -ForegroundColor Yellow
try {
    $result = & "D:\esp32-idf-ahy\5.3.2\python_env\idf5.3_py3.11_env\Scripts\python.exe" "$env:IDF_PATH\tools\idf.py" build 2>&1
    Write-Host $result
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed with exit code $LASTEXITCODE" -ForegroundColor Red
        exit 1
    }
    Write-Host "✓ Build completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Build error: $_" -ForegroundColor Red
    exit 1
}

# Check if build directory exists
if (Test-Path "build") {
    Write-Host "✓ Build directory created" -ForegroundColor Green
    
    # Check if xiaozhi.bin exists
    if (Test-Path "build\xiaozhi.bin") {
        Write-Host "✓ Firmware binary created: xiaozhi.bin" -ForegroundColor Green
        $binSize = (Get-Item "build\xiaozhi.bin").Length
        Write-Host "Binary size: $binSize bytes" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Firmware binary not found" -ForegroundColor Red
    }
    
    # Check if WiFi data receiver component was compiled
    if (Test-Path "build\esp-idf\wifi_data_receiver") {
        Write-Host "✓ WiFi data receiver component compiled" -ForegroundColor Green
    } else {
        Write-Host "⚠ WiFi data receiver component not found in build" -ForegroundColor Yellow
        Write-Host "This might be normal if the component is statically linked" -ForegroundColor Gray
    }
} else {
    Write-Host "✗ Build directory not created" -ForegroundColor Red
    exit 1
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "Build Summary:" -ForegroundColor Green
Write-Host "- Target: ESP32S3" -ForegroundColor Cyan
Write-Host "- WiFi Data Receiver: Integrated" -ForegroundColor Cyan
Write-Host "- TCP Server Port: 8080" -ForegroundColor Cyan
Write-Host "- Max Clients: 3" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Green

Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Flash the firmware: idf.py flash" -ForegroundColor White
Write-Host "2. Monitor serial output: idf.py monitor" -ForegroundColor White
Write-Host "3. Configure WiFi on first boot" -ForegroundColor White
Write-Host "4. Note the IP address from serial output" -ForegroundColor White
Write-Host "5. Configure PlatformIO sender with the IP address" -ForegroundColor White
