# ESP32S3-PICO WiFi数据接收端部署指南

## 🎯 项目概述

本项目已成功为ESP32S3-PICO设备集成了WiFi数据接收功能，可以作为TCP服务器接收来自PlatformIO发送端的数据。

### ✨ 主要功能
- 🌐 TCP服务器（端口8080）
- 📱 多客户端支持（最多3个）
- 📊 JSON数据解析
- 🔔 实时通知显示
- 📝 详细日志记录
- 🔄 自动重连机制

## 🚀 快速部署

### 步骤1: 编译和烧录接收端

1. **配置目标芯片**：
   ```bash
   idf.py set-target esp32s3
   ```

2. **配置板型**：
   ```bash
   idf.py menuconfig
   ```
   选择：`Xiaozhi Assistant -> Board Type -> [您的ESP32S3板型]`

3. **编译项目**：
   ```bash
   idf.py build
   ```

4. **烧录到设备**：
   ```bash
   idf.py flash monitor
   ```

### 步骤2: 配置WiFi连接

**方法A: 自动配置（推荐）**
1. 设备首次启动会自动进入WiFi配置模式
2. 创建热点"Xiaozhi-XXXXXX"
3. 手机连接该热点
4. 浏览器访问 `http://***********`
5. 选择WiFi网络并输入密码

**方法B: 手动重置WiFi**
- 启动时按住BOOT按钮
- 或触摸屏幕进入配置模式

### 步骤3: 获取接收端IP地址

连接串口监视器（115200波特率），查找以下输出：

```
I (12345) Application: 📡 WiFi Data Receiver started successfully!
I (12346) Application: 🌐 Server IP: *************
I (12347) Application: 🔌 Server Port: 8080
I (12348) Application: 📋 Configuration for PlatformIO sender:
I (12349) Application:    wifi_serial_target_ip = "*************"
I (12350) Application:    wifi_serial_target_port = 8080
```

**记录这个IP地址，稍后配置发送端时需要使用！**

## 🔧 PlatformIO发送端配置

### 创建PlatformIO项目

1. **创建新项目**：
   - 平台：Espressif 32
   - 板型：ESP32S3 Dev Module（或您的具体板型）
   - 框架：Arduino

2. **配置platformio.ini**：
   ```ini
   [env:esp32s3dev]
   platform = espressif32
   board = esp32s3dev
   framework = arduino
   monitor_speed = 115200
   lib_deps = 
       bblanchon/ArduinoJson@^6.21.3
   ```

3. **创建发送端代码**：

<augment_code_snippet path="platformio_sender_example.cpp" mode="EXCERPT">
````cpp
#include <WiFi.h>
#include <WiFiClient.h>
#include <ArduinoJson.h>

// WiFi配置
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// ESP32S3接收端配置（替换为实际IP地址）
const char* wifi_serial_target_ip = "*************";
const int wifi_serial_target_port = 8080;

WiFiClient client;
unsigned long lastSendTime = 0;
const unsigned long sendInterval = 5000; // 5秒发送一次

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("🚀 ESP32S3 WiFi数据发送端启动");
    
    // 连接WiFi
    WiFi.begin(ssid, password);
    Serial.print("连接WiFi");
    
    while (WiFi.status() != WL_CONNECTED) {
        delay(500);
        Serial.print(".");
    }
    
    Serial.println();
    Serial.println("✅ WiFi连接成功!");
    Serial.print("本机IP地址: ");
    Serial.println(WiFi.localIP());
    Serial.printf("目标服务器: %s:%d\n", wifi_serial_target_ip, wifi_serial_target_port);
}

void loop() {
    if (millis() - lastSendTime >= sendInterval) {
        sendSensorData();
        lastSendTime = millis();
    }
    
    delay(100);
}

void sendSensorData() {
    // 创建JSON数据
    StaticJsonDocument<512> doc;
    doc["timestamp"] = millis() / 1000;
    doc["message"] = "Hello from PlatformIO sender!";
    doc["device_status"] = "normal";
    
    // 模拟传感器数据
    JsonObject sensor_data = doc.createNestedObject("sensor_data");
    sensor_data["temperature"] = 20.0 + random(0, 100) / 10.0;
    sensor_data["humidity"] = 40.0 + random(0, 400) / 10.0;
    sensor_data["pressure"] = 1000.0 + random(0, 500) / 10.0;
    
    // 序列化JSON
    String jsonString;
    serializeJson(doc, jsonString);
    
    // 发送数据
    if (client.connect(wifi_serial_target_ip, wifi_serial_target_port)) {
        Serial.println("📤 发送数据到接收端:");
        Serial.println(jsonString);
        
        client.print(jsonString);
        client.stop();
        
        Serial.println("✅ 数据发送完成");
    } else {
        Serial.println("❌ 连接接收端失败");
    }
}
````
</augment_code_snippet>

## 🧪 测试验证

### 方法1: 使用Python测试脚本

```bash
# 安装Python（如果尚未安装）
# 运行测试脚本
python test_wifi_receiver.py --host ************* --port 8080 --test all
```

### 方法2: 手动测试

使用telnet或netcat工具：

```bash
# 发送JSON数据
echo '{"message":"Test from command line","timestamp":1234567890}' | nc ************* 8080

# 发送文本数据
echo "Hello ESP32S3!" | nc ************* 8080
```

### 方法3: 使用PlatformIO发送端

1. 修改发送端代码中的IP地址
2. 编译并烧录到另一个ESP32设备
3. 观察两个设备的串口输出

## 📊 预期结果

### 接收端串口输出示例：
```
I (15234) Application: === WiFi Data Received ===
I (15235) Application: From: *************:54321
I (15236) Application: Length: 156 bytes
I (15237) Application: Data: {"timestamp":1234567890,"message":"Hello!","sensor_data":{"temperature":25.6}}
I (15238) Application: === JSON Data Parsed ===
I (15239) Application: Timestamp: 1234567890
I (15240) Application: Temperature: 25.60°C
I (15241) Application: Message: Hello!
```

### 接收端屏幕显示：
- 客户端连接通知
- 接收到的消息内容
- 传感器数据（如果有）

## 🔍 故障排除

### 问题1: WiFi连接失败
**解决方案**：
- 检查WiFi密码
- 确认网络支持2.4GHz
- 重启路由器

### 问题2: 无法获取IP地址
**解决方案**：
- 检查DHCP设置
- 手动设置静态IP
- 查看串口错误信息

### 问题3: 发送端连接失败
**解决方案**：
- 确认IP地址正确
- 检查端口8080是否被占用
- 验证两设备在同一网络

### 问题4: 数据接收异常
**解决方案**：
- 检查JSON格式
- 确认数据编码为UTF-8
- 查看详细错误日志

## 📈 性能优化

### 网络优化
- 使用5GHz WiFi（如果支持）
- 减少网络延迟
- 优化数据包大小

### 内存优化
- 限制JSON数据大小
- 及时释放内存
- 监控堆栈使用

## 🔧 高级配置

### 修改服务器参数

在 `main/application.cc` 的 `InitializeWifiDataReceiver()` 函数中：

```cpp
wifi_data_receiver_config_t config = wifi_data_receiver_get_default_config();
config.port = 8080;           // 修改端口
config.max_clients = 5;       // 修改最大客户端数
config.recv_timeout_ms = 15000; // 修改超时时间
```

### 添加自定义数据处理

在 `OnWifiDataReceived()` 回调函数中添加您的处理逻辑。

## 📞 技术支持

如遇到问题，请提供：
1. 完整的串口日志
2. 网络配置信息
3. 发送的数据格式
4. 错误现象描述

---

🎉 **恭喜！您已成功部署ESP32S3-PICO WiFi数据接收端！**
