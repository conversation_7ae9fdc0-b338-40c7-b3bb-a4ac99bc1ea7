#include "ble_manager.h"
#include "esp_bt_main.h"
#include "esp_bt.h"
#include "nvs_flash.h"
#include <cstring>
#include <iomanip>
#include <sstream>

static const char* TAG = "BleManager";

BleManager* BleManager::instance_ = nullptr;

std::string BleManager::BdAddrToString(const esp_bd_addr_t& addr) {
    std::stringstream ss;
    for (int i = 0; i < 6; i++) {
        if (i > 0) ss << ":";
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(addr[i]);
    }
    return ss.str();
}

void BleManager::StringToBdAddr(const std::string& addr_str, esp_bd_addr_t& addr) {
    std::stringstream ss(addr_str);
    std::string byte_str;
    int i = 0;
    
    while (std::getline(ss, byte_str, ':') && i < 6) {
        addr[i++] = static_cast<uint8_t>(std::stoi(byte_str, nullptr, 16));
    }
}

std::string BleManager::ConnectionStateToString(BleConnectionState state) {
    switch (state) {
        case BLE_DISCONNECTED: return "DISCONNECTED";
        case BLE_CONNECTING: return "CONNECTING";
        case BLE_CONNECTED: return "CONNECTED";
        case BLE_DISCONNECTING: return "DISCONNECTING";
        default: return "UNKNOWN";
    }
}

bool BleManager::InitializeBluetooth() {
    ESP_LOGI(TAG, "Initializing Bluetooth controller");
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 释放经典蓝牙内存（我们只使用BLE）
    ESP_ERROR_CHECK(esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT));
    
    // 初始化蓝牙控制器
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 启用BLE模式
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 初始化Bluedroid
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to init bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    ESP_LOGI(TAG, "Bluetooth controller initialized successfully");
    return true;
}

bool BleManager::InitializeBle() {
    ESP_LOGI(TAG, "Initializing BLE");
    
    // 注册GAP回调
    esp_err_t ret = esp_ble_gap_register_callback(GapEventHandler);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GAP callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 注册GATTS回调
    ret = esp_ble_gatts_register_callback(GattsEventHandler);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GATTS callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 注册GATTC回调
    ret = esp_ble_gattc_register_callback(GattcEventHandler);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GATTC callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 设置MTU大小
    ret = esp_ble_gatt_set_local_mtu(517);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set local MTU: %s", esp_err_to_name(ret));
        return false;
    }
    
    ESP_LOGI(TAG, "BLE initialized successfully");
    return true;
}

void BleManager::GapEventHandler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGapEvent(event, param);
    }
}

void BleManager::GattsEventHandler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGattsEvent(event, gatts_if, param);
    }
}

void BleManager::GattcEventHandler(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_ble_gattc_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGattcEvent(event, gattc_if, param);
    }
}
