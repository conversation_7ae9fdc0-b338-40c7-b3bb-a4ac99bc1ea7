#include "bluetooth_server.h"
#include "esp_log.h"
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_bt_device.h"
#include <cstring>

const char* BluetoothServer::SERVER_NAME = "ESP32-S3-PICO-Server";
const char* BluetoothServer::SPP_SERVICE_NAME = "SPP_SERVER";
const esp_spp_sec_t BluetoothServer::SPP_SEC_MASK = ESP_SPP_SEC_AUTHENTICATE;
const esp_spp_role_t BluetoothServer::SPP_ROLE = ESP_SPP_ROLE_SLAVE;

BluetoothServer* BluetoothServer::instance_ = nullptr;

BluetoothServer::BluetoothServer(const std::string& device_name)
    : BluetoothManager()
    , server_started_(false)
    , discoverable_(false)
    , connectable_(false)
    , spp_handle_(0)
    , client_connected_(false)
    , device_name_(device_name)
    , use_pin_code_(false) {
    instance_ = this;
    memset(pin_code_, 0, sizeof(pin_code_));
    memset(connected_device_, 0, sizeof(connected_device_));
}

BluetoothServer::~BluetoothServer() {
    StopServer();
    Deinitialize();
    instance_ = nullptr;
}

bool BluetoothServer::Initialize() {
    ESP_LOGI(TAG, "Initializing Bluetooth Server");
    
    if (initialized_) {
        ESP_LOGW(TAG, "Bluetooth Server already initialized");
        return true;
    }
    
    // 初始化蓝牙控制器
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    esp_err_t ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bt_controller_enable(ESP_BT_MODE_CLASSIC_BT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 初始化蓝牙栈
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 初始化GAP和SPP
    if (!InitializeGap() || !InitializeSpp()) {
        ESP_LOGE(TAG, "Failed to initialize GAP or SPP");
        return false;
    }
    
    // 设置设备属性
    if (!SetDeviceProperties()) {
        ESP_LOGE(TAG, "Failed to set device properties");
        return false;
    }
    
    initialized_ = true;
    ESP_LOGI(TAG, "Bluetooth Server initialized successfully");
    return true;
}

bool BluetoothServer::InitializeGap() {
    esp_err_t ret = esp_bt_gap_register_callback(StaticGapCallback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GAP callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 设置扫描模式
    ret = esp_bt_gap_set_scan_mode(ESP_BT_CONNECTABLE, ESP_BT_GENERAL_DISCOVERABLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set scan mode: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool BluetoothServer::InitializeSpp() {
    esp_err_t ret = esp_spp_register_callback(StaticSppCallback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register SPP callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_spp_init(ESP_SPP_MODE_CB);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize SPP: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool BluetoothServer::SetDeviceProperties() {
    // 设置设备名称
    esp_err_t ret = esp_bt_dev_set_device_name(device_name_.c_str());
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set device name: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 设置PIN码（如果启用）
    if (use_pin_code_) {
        ret = esp_bt_gap_set_pin(ESP_BT_PIN_TYPE_FIXED, sizeof(pin_code_), pin_code_);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to set PIN code: %s", esp_err_to_name(ret));
            return false;
        }
    }
    
    return true;
}

bool BluetoothServer::StartServer() {
    if (!initialized_) {
        ESP_LOGE(TAG, "Bluetooth not initialized");
        return false;
    }
    
    if (server_started_) {
        ESP_LOGW(TAG, "Server already started");
        return true;
    }
    
    ESP_LOGI(TAG, "Starting SPP server");
    
    esp_err_t ret = esp_spp_start_srv(SPP_SEC_MASK, SPP_ROLE, 0, SPP_SERVICE_NAME);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start SPP server: %s", esp_err_to_name(ret));
        return false;
    }
    
    server_started_ = true;
    ESP_LOGI(TAG, "SPP server started successfully");
    return true;
}

bool BluetoothServer::StopServer() {
    if (!server_started_) {
        return true;
    }
    
    ESP_LOGI(TAG, "Stopping SPP server");
    server_started_ = false;
    return true;
}

bool BluetoothServer::StartDiscovery() {
    ESP_LOGW(TAG, "Server mode: Discovery not applicable");
    return false;
}

bool BluetoothServer::StopDiscovery() {
    ESP_LOGW(TAG, "Server mode: Discovery not applicable");
    return false;
}

bool BluetoothServer::Connect(const esp_bd_addr_t& address) {
    ESP_LOGW(TAG, "Server mode: Outgoing connections not supported");
    return false;
}

bool BluetoothServer::SetDiscoverable(bool discoverable) {
    discoverable_ = discoverable;
    
    if (!initialized_) {
        return false;
    }
    
    esp_bt_scan_mode_t mode = discoverable ? ESP_BT_GENERAL_DISCOVERABLE : ESP_BT_NON_DISCOVERABLE;
    esp_err_t ret = esp_bt_gap_set_scan_mode(ESP_BT_CONNECTABLE, mode);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set discoverable mode: %s", esp_err_to_name(ret));
        return false;
    }
    
    ESP_LOGI(TAG, "Discoverable mode set to: %s", discoverable ? "ON" : "OFF");
    return true;
}

bool BluetoothServer::SetConnectable(bool connectable) {
    connectable_ = connectable;
    
    if (!initialized_) {
        return false;
    }
    
    esp_bt_scan_mode_t conn_mode = connectable ? ESP_BT_CONNECTABLE : ESP_BT_NON_CONNECTABLE;
    esp_bt_scan_mode_t disc_mode = discoverable_ ? ESP_BT_GENERAL_DISCOVERABLE : ESP_BT_NON_DISCOVERABLE;
    
    esp_err_t ret = esp_bt_gap_set_scan_mode(conn_mode, disc_mode);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set connectable mode: %s", esp_err_to_name(ret));
        return false;
    }
    
    ESP_LOGI(TAG, "Connectable mode set to: %s", connectable ? "ON" : "OFF");
    return true;
}

void BluetoothServer::SetDeviceName(const std::string& name) {
    device_name_ = name;
    if (initialized_) {
        esp_bt_dev_set_device_name(device_name_.c_str());
    }
}

void BluetoothServer::SetPinCode(const std::string& pin) {
    if (pin.length() > sizeof(pin_code_)) {
        ESP_LOGW(TAG, "PIN code too long, truncating");
    }
    
    memset(pin_code_, 0, sizeof(pin_code_));
    strncpy((char*)pin_code_, pin.c_str(), sizeof(pin_code_) - 1);
    use_pin_code_ = true;
    
    if (initialized_) {
        esp_bt_gap_set_pin(ESP_BT_PIN_TYPE_FIXED, pin.length(), pin_code_);
    }
}

void BluetoothServer::DisablePinCode() {
    use_pin_code_ = false;
    memset(pin_code_, 0, sizeof(pin_code_));
}

void BluetoothServer::StaticGapCallback(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGapEvent(event, param);
    }
}

void BluetoothServer::StaticSppCallback(esp_spp_cb_event_t event, esp_spp_cb_param_t* param) {
    if (instance_) {
        instance_->HandleSppEvent(event, param);
    }
}

void BluetoothServer::HandleGapEvent(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t* param) {
    switch (event) {
        case ESP_BT_GAP_AUTH_CMPL_EVT:
            if (param->auth_cmpl.stat == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "Authentication complete: %s", param->auth_cmpl.device_name);
            } else {
                ESP_LOGE(TAG, "Authentication failed, status: %d", param->auth_cmpl.stat);
            }
            break;
            
        case ESP_BT_GAP_PIN_REQ_EVT:
            ESP_LOGI(TAG, "PIN request for device: %s", param->pin_req.device_name);
            if (use_pin_code_) {
                esp_bt_gap_pin_reply(param->pin_req.bda, true, sizeof(pin_code_), pin_code_);
            }
            break;
            
        default:
            ESP_LOGD(TAG, "GAP event: %d", event);
            break;
    }
}

void BluetoothServer::HandleSppEvent(esp_spp_cb_event_t event, esp_spp_cb_param_t* param) {
    switch (event) {
        case ESP_SPP_INIT_EVT:
            ESP_LOGI(TAG, "SPP initialized");
            break;
            
        case ESP_SPP_START_EVT:
            ESP_LOGI(TAG, "SPP server started");
            break;
            
        case ESP_SPP_SRV_OPEN_EVT:
            ESP_LOGI(TAG, "SPP server connection opened");
            OnClientConnected(param->srv_open.handle, param->srv_open.rem_bda);
            break;

        case ESP_SPP_CLOSE_EVT:
            ESP_LOGI(TAG, "SPP connection closed");
            OnClientDisconnected();
            break;

        case ESP_SPP_DATA_IND_EVT:
            ESP_LOGD(TAG, "SPP data received: %d bytes", param->data_ind.len);
            OnDataReceived(param->data_ind.data, param->data_ind.len);
            break;
            
        default:
            ESP_LOGD(TAG, "SPP event: %d", event);
            break;
    }
}

// 数据传输方法实现
bool BluetoothServer::SendData(const uint8_t* data, size_t length) {
    if (!client_connected_ || spp_handle_ == 0) {
        ESP_LOGW(TAG, "No client connected, cannot send data");
        return false;
    }

    esp_err_t ret = esp_spp_write(spp_handle_, length, (uint8_t*)data);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to send data: %s", esp_err_to_name(ret));
        return false;
    }

    ESP_LOGD(TAG, "Sent %d bytes to client", length);
    return true;
}

bool BluetoothServer::SendString(const std::string& message) {
    return SendData((const uint8_t*)message.c_str(), message.length());
}

// 回调设置方法
void BluetoothServer::SetDataReceivedCallback(data_received_callback_t callback) {
    data_callback_ = callback;
}

void BluetoothServer::SetConnectionStatusCallback(connection_status_callback_t callback) {
    connection_callback_ = callback;
}

// 连接管理方法
void BluetoothServer::OnClientConnected(uint32_t handle, const esp_bd_addr_t& address) {
    spp_handle_ = handle;
    client_connected_ = true;
    memcpy(connected_device_, address, sizeof(esp_bd_addr_t));

    ESP_LOGI(TAG, "Client connected: %02x:%02x:%02x:%02x:%02x:%02x",
             address[0], address[1], address[2], address[3], address[4], address[5]);

    if (connection_callback_) {
        connection_callback_(true, address);
    }
}

void BluetoothServer::OnClientDisconnected() {
    client_connected_ = false;
    spp_handle_ = 0;

    ESP_LOGI(TAG, "Client disconnected");

    if (connection_callback_) {
        esp_bd_addr_t empty_addr = {0};
        connection_callback_(false, empty_addr);
    }

    memset(connected_device_, 0, sizeof(connected_device_));
}

void BluetoothServer::OnDataReceived(const uint8_t* data, size_t length) {
    ESP_LOGD(TAG, "Data received: %d bytes", length);

    if (data_callback_) {
        data_callback_(data, length);
    }
}
