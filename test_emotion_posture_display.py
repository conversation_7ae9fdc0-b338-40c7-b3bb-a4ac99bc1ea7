#!/usr/bin/env python3
"""
测试表情与坐姿状态显示功能
发送模拟的表情和坐姿数据到ESP32S3接收端
"""

import socket
import json
import time
import random

# ESP32S3接收端配置
ESP32_IP = "**************"  # 根据实际情况修改
ESP32_PORT = 8080

# 测试数据
emotions = ["Happy", "Sad", "Angry", "Excited", "Calm"]
postures = ["Good Posture", "Poor Posture"]

def send_emotion_posture_data(sock, emotion, posture, confidence=0.85):
    """发送表情和坐姿数据"""
    data = {
        "emotion": emotion,
        "posture": posture,
        "confidence": confidence,
        "timestamp": int(time.time())
    }
    
    json_data = json.dumps(data)
    print(f"发送数据: {json_data}")
    
    try:
        sock.send(json_data.encode('utf-8'))
        print("✅ 数据发送成功")
        return True
    except Exception as e:
        print(f"❌ 发送失败: {e}")
        return False

def main():
    print("🎭 表情与坐姿状态显示测试")
    print(f"目标设备: {ESP32_IP}:{ESP32_PORT}")
    print("=" * 50)
    
    try:
        # 连接到ESP32S3
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((ESP32_IP, ESP32_PORT))
        print(f"✅ 成功连接到 {ESP32_IP}:{ESP32_PORT}")
        
        # 测试序列
        test_cases = [
            ("Happy", "Good Posture", 0.95),
            ("Sad", "Poor Posture", 0.80),
            ("Angry", "Poor Posture", 0.90),
            ("Excited", "Good Posture", 0.85),
            ("Calm", "Good Posture", 0.92),
        ]
        
        print("\n开始测试序列...")
        for i, (emotion, posture, confidence) in enumerate(test_cases, 1):
            print(f"\n--- 测试 {i}/5 ---")
            print(f"表情: {emotion}, 坐姿: {posture}, 置信度: {confidence}")
            
            if send_emotion_posture_data(sock, emotion, posture, confidence):
                print("⏳ 等待3秒...")
                time.sleep(3)
            else:
                break
        
        print("\n🎯 随机测试模式 (按Ctrl+C停止)")
        while True:
            emotion = random.choice(emotions)
            posture = random.choice(postures)
            confidence = round(random.uniform(0.7, 0.98), 2)
            
            print(f"\n随机测试: {emotion} + {posture} (置信度: {confidence})")
            if not send_emotion_posture_data(sock, emotion, posture, confidence):
                break
            
            time.sleep(5)  # 每5秒发送一次
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except ConnectionRefusedError:
        print(f"❌ 无法连接到 {ESP32_IP}:{ESP32_PORT}")
        print("请确保:")
        print("1. ESP32S3设备已启动并连接到WiFi")
        print("2. IP地址正确")
        print("3. WiFi数据接收服务器正在运行")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    finally:
        try:
            sock.close()
            print("🔌 连接已关闭")
        except:
            pass

if __name__ == "__main__":
    main()
