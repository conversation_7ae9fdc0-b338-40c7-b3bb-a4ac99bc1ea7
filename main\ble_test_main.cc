#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "bluetooth/ble_server.h"
#include "bluetooth/ble_client.h"

static const char* TAG = "BLE_TEST";

// 测试BLE服务器
void test_ble_server() {
    ESP_LOGI(TAG, "Testing BLE Server");
    
    BleServer* server = BleServer::GetInstance();
    if (!server) {
        ESP_LOGE(TAG, "Failed to get BLE server instance");
        return;
    }
    
    // 设置设备名称
    server->SetDeviceName("ESP32-S3-BLE-Test");
    
    // 设置数据接收回调
    server->SetDataCallback([](const uint8_t* data, size_t length) {
        ESP_LOGI(TAG, "Received data: %.*s", (int)length, (char*)data);
    });
    
    // 设置连接状态回调
    server->SetConnectionCallback([](BleConnectionState state, const esp_bd_addr_t& address) {
        ESP_LOGI(TAG, "Connection state changed: %s", 
                 BleManager::ConnectionStateToString(state).c_str());
    });
    
    // 初始化服务器
    if (!server->Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize BLE server");
        return;
    }
    
    // 开始广告
    if (!server->StartAdvertising()) {
        ESP_LOGE(TAG, "Failed to start advertising");
        return;
    }
    
    ESP_LOGI(TAG, "BLE Server started successfully");
    
    // 测试数据发送
    int counter = 0;
    while (true) {
        vTaskDelay(pdMS_TO_TICKS(5000));
        
        if (server->IsClientConnected()) {
            char message[64];
            snprintf(message, sizeof(message), "Hello from BLE Server #%d", counter++);
            
            if (server->SendData(message)) {
                ESP_LOGI(TAG, "Sent: %s", message);
            } else {
                ESP_LOGE(TAG, "Failed to send data");
            }
        } else {
            ESP_LOGI(TAG, "No client connected, waiting...");
        }
    }
}

// 测试BLE客户端
void test_ble_client() {
    ESP_LOGI(TAG, "Testing BLE Client");
    
    BleClient* client = BleClient::GetInstance();
    if (!client) {
        ESP_LOGE(TAG, "Failed to get BLE client instance");
        return;
    }
    
    // 设置设备名称
    client->SetDeviceName("ESP32-S3-BLE-Client");
    
    // 设置数据接收回调
    client->SetDataCallback([](const uint8_t* data, size_t length) {
        ESP_LOGI(TAG, "Received data: %.*s", (int)length, (char*)data);
    });
    
    // 设置连接状态回调
    client->SetConnectionCallback([](BleConnectionState state, const esp_bd_addr_t& address) {
        ESP_LOGI(TAG, "Connection state changed: %s", 
                 BleManager::ConnectionStateToString(state).c_str());
    });
    
    // 初始化客户端
    if (!client->Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize BLE client");
        return;
    }
    
    ESP_LOGI(TAG, "BLE Client initialized successfully");
    
    // 扫描设备
    while (true) {
        ESP_LOGI(TAG, "Starting scan...");
        if (!client->StartScan(10)) {
            ESP_LOGE(TAG, "Failed to start scan");
            vTaskDelay(pdMS_TO_TICKS(5000));
            continue;
        }
        
        // 等待扫描完成
        while (client->IsScanning()) {
            vTaskDelay(pdMS_TO_TICKS(1000));
        }
        
        // 显示发现的设备
        auto devices = client->GetDiscoveredDevices();
        ESP_LOGI(TAG, "Found %d devices:", devices.size());
        
        for (const auto& device : devices) {
            ESP_LOGI(TAG, "Device: %s, RSSI: %d", 
                     BleManager::BdAddrToString(device.address).c_str(), device.rssi);
        }
        
        // 尝试连接第一个设备
        if (!devices.empty()) {
            ESP_LOGI(TAG, "Attempting to connect to first device...");
            if (client->Connect(devices[0].address)) {
                ESP_LOGI(TAG, "Connection initiated");
                
                // 等待连接建立
                int timeout = 30; // 30秒超时
                while (timeout > 0 && client->GetConnectionState() == BLE_CONNECTING) {
                    vTaskDelay(pdMS_TO_TICKS(1000));
                    timeout--;
                }
                
                if (client->GetConnectionState() == BLE_CONNECTED) {
                    ESP_LOGI(TAG, "Connected successfully!");
                    
                    // 发送测试数据
                    int counter = 0;
                    while (client->GetConnectionState() == BLE_CONNECTED) {
                        char message[64];
                        snprintf(message, sizeof(message), "Hello from BLE Client #%d", counter++);
                        
                        if (client->SendData(message)) {
                            ESP_LOGI(TAG, "Sent: %s", message);
                        } else {
                            ESP_LOGE(TAG, "Failed to send data");
                        }
                        
                        vTaskDelay(pdMS_TO_TICKS(3000));
                    }
                } else {
                    ESP_LOGE(TAG, "Failed to connect");
                }
            }
        }
        
        vTaskDelay(pdMS_TO_TICKS(10000)); // 等待10秒后重新扫描
    }
}

extern "C" void app_main() {
    ESP_LOGI(TAG, "BLE Test Application Starting");
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 根据配置选择测试模式
#ifdef CONFIG_BLE_TEST_SERVER
    ESP_LOGI(TAG, "Running in BLE Server mode");
    test_ble_server();
#elif defined(CONFIG_BLE_TEST_CLIENT)
    ESP_LOGI(TAG, "Running in BLE Client mode");
    test_ble_client();
#else
    ESP_LOGI(TAG, "Running in BLE Server mode (default)");
    test_ble_server();
#endif
}
