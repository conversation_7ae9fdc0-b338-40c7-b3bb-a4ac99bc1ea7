#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"
#include "bluetooth/ble_client.h"

static const char* TAG = "BLE_CLIENT_TEST";

// BLE事件回调
void OnBleDataReceived(const uint8_t* data, size_t length) {
    ESP_LOGI(TAG, "BLE data received: %zu bytes", length);
    std::string data_str(reinterpret_cast<const char*>(data), length);
    ESP_LOGI(TAG, "BLE data content: %s", data_str.c_str());
}

void OnBleConnectionChanged(BleConnectionState state, const esp_bd_addr_t& address) {
    std::string addr_str = BleManager::BdAddrToString(address);
    ESP_LOGI(TAG, "BLE connection state changed: %s, device: %s", 
             BleManager::ConnectionStateToString(state).c_str(), addr_str.c_str());
    
    switch (state) {
        case BLE_CONNECTED:
            ESP_LOGI(TAG, "✅ Connected to DevKitC successfully!");
            break;
        case BLE_DISCONNECTED:
            ESP_LOGI(TAG, "❌ Disconnected from DevKitC");
            break;
        case BLE_CONNECTING:
            ESP_LOGI(TAG, "🔄 Connecting to DevKitC...");
            break;
        default:
            break;
    }
}

// 扫描完成后尝试连接
void TryConnectToTarget(BleClient* client) {
    auto devices = client->GetDiscoveredDevices();
    ESP_LOGI(TAG, "Found %d BLE devices", devices.size());
    
    for (const auto& device : devices) {
        std::string addr_str = BleManager::BdAddrToString(device.address);
        ESP_LOGI(TAG, "Device: %s, Name: %s, RSSI: %d", 
                 addr_str.c_str(), device.name.c_str(), device.rssi);
        
        // 查找目标设备
        if (device.name.find("XiaoZhi") != std::string::npos || 
            device.name.find("DevKitC") != std::string::npos ||
            device.name.find("ESP32") != std::string::npos) {
            
            ESP_LOGI(TAG, "Found target device: %s", addr_str.c_str());
            
            if (client->Connect(device.address)) {
                ESP_LOGI(TAG, "Connecting to target device...");
                return;
            } else {
                ESP_LOGE(TAG, "Failed to initiate connection");
            }
        }
    }
    
    ESP_LOGW(TAG, "Target device not found, will retry scan in 5 seconds");
    vTaskDelay(pdMS_TO_TICKS(5000));
    client->StartScan(10);
}

extern "C" void app_main() {
    ESP_LOGI(TAG, "Starting BLE Client Test");
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 获取BLE客户端实例
    auto ble_client = BleClient::GetInstance();
    if (!ble_client) {
        ESP_LOGE(TAG, "Failed to get BLE client instance");
        return;
    }
    
    // 设置设备名称
    ble_client->SetDeviceName("XiaoZhi-PICO-Client");
    
    // 设置回调
    ble_client->SetDataCallback(OnBleDataReceived);
    ble_client->SetConnectionCallback(OnBleConnectionChanged);
    
    // 初始化BLE客户端
    if (!ble_client->Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize BLE client");
        return;
    }
    
    ESP_LOGI(TAG, "BLE client initialized successfully");
    
    // 开始扫描
    ESP_LOGI(TAG, "Starting BLE device scan...");
    if (!ble_client->StartScan(10)) {
        ESP_LOGE(TAG, "Failed to start BLE scan");
        return;
    }
    
    // 主循环
    int scan_count = 0;
    while (true) {
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 检查扫描状态
        if (!ble_client->IsScanning() && ble_client->GetConnectionState() == BLE_DISCONNECTED) {
            scan_count++;
            ESP_LOGI(TAG, "Scan completed (%d), attempting to connect...", scan_count);
            TryConnectToTarget(ble_client);
        }
        
        // 如果已连接，发送测试数据
        if (ble_client->GetConnectionState() == BLE_CONNECTED) {
            static int test_count = 0;
            test_count++;
            
            if (test_count % 10 == 0) {  // 每10秒发送一次测试数据
                std::string test_data = "Test message " + std::to_string(test_count);
                ESP_LOGI(TAG, "Sending test data: %s", test_data.c_str());
                ble_client->SendData((const uint8_t*)test_data.c_str(), test_data.length());
            }
        }
    }
}
