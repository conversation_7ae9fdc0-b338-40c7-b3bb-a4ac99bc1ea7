#!/usr/bin/env python3
"""
WiFi数据接收端测试脚本

用于测试ESP32S3-PICO WiFi数据接收功能
模拟PlatformIO发送端发送各种格式的数据
"""

import socket
import json
import time
import argparse
import sys
from datetime import datetime

def send_json_data(host, port, data):
    """发送JSON数据到ESP32S3接收端"""
    try:
        # 创建TCP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        # 连接到ESP32S3
        print(f"🔗 连接到 {host}:{port}...")
        sock.connect((host, port))
        print("✅ 连接成功!")
        
        # 发送数据
        json_str = json.dumps(data, ensure_ascii=False)
        print(f"📤 发送数据: {json_str}")
        sock.send(json_str.encode('utf-8'))
        
        # 等待一下确保数据发送完成
        time.sleep(0.5)
        
        print("✅ 数据发送完成")
        
    except socket.timeout:
        print("❌ 连接超时")
    except ConnectionRefusedError:
        print("❌ 连接被拒绝，请检查ESP32S3是否启动并连接到网络")
    except Exception as e:
        print(f"❌ 发送失败: {e}")
    finally:
        sock.close()

def send_text_data(host, port, text):
    """发送纯文本数据到ESP32S3接收端"""
    try:
        # 创建TCP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        # 连接到ESP32S3
        print(f"🔗 连接到 {host}:{port}...")
        sock.connect((host, port))
        print("✅ 连接成功!")
        
        # 发送数据
        print(f"📤 发送文本: {text}")
        sock.send(text.encode('utf-8'))
        
        # 等待一下确保数据发送完成
        time.sleep(0.5)
        
        print("✅ 数据发送完成")
        
    except socket.timeout:
        print("❌ 连接超时")
    except ConnectionRefusedError:
        print("❌ 连接被拒绝，请检查ESP32S3是否启动并连接到网络")
    except Exception as e:
        print(f"❌ 发送失败: {e}")
    finally:
        sock.close()

def test_sensor_data(host, port):
    """测试传感器数据"""
    print("\n🌡️ 测试传感器数据...")
    
    sensor_data = {
        "timestamp": int(time.time()),
        "sensor_data": {
            "temperature": 25.6,
            "humidity": 60.2,
            "pressure": 1013.25
        },
        "device_status": "normal",
        "message": "传感器数据正常"
    }
    
    send_json_data(host, port, sensor_data)

def test_message_data(host, port):
    """测试消息数据"""
    print("\n💬 测试消息数据...")
    
    message_data = {
        "timestamp": int(time.time()),
        "message": "Hello from Python test script!",
        "device_status": "testing",
        "test_id": "msg_001"
    }
    
    send_json_data(host, port, message_data)

def test_complex_data(host, port):
    """测试复杂数据"""
    print("\n🔧 测试复杂数据...")
    
    complex_data = {
        "timestamp": int(time.time()),
        "device_info": {
            "name": "Test Device",
            "version": "1.0.0",
            "mac": "AA:BB:CC:DD:EE:FF"
        },
        "sensor_data": {
            "temperature": 28.3,
            "humidity": 55.8,
            "pressure": 1015.2,
            "light": 450,
            "motion": True
        },
        "status": {
            "battery": 85,
            "signal": -45,
            "uptime": 3600
        },
        "message": "复杂数据测试",
        "alerts": ["温度正常", "湿度正常"],
        "device_status": "active"
    }
    
    send_json_data(host, port, complex_data)

def test_text_data(host, port):
    """测试纯文本数据"""
    print("\n📝 测试纯文本数据...")
    
    texts = [
        "Hello ESP32S3!",
        "这是中文测试消息",
        "Test message with numbers: 12345",
        "Special chars: !@#$%^&*()",
        "Multi-line\ntext\nmessage"
    ]
    
    for text in texts:
        send_text_data(host, port, text)
        time.sleep(1)

def test_continuous_data(host, port, count=5):
    """测试连续数据发送"""
    print(f"\n🔄 测试连续数据发送 ({count}次)...")
    
    for i in range(count):
        data = {
            "timestamp": int(time.time()),
            "sequence": i + 1,
            "sensor_data": {
                "temperature": 20.0 + i * 2.5,
                "humidity": 50.0 + i * 5.0,
                "pressure": 1010.0 + i * 2.0
            },
            "message": f"连续测试消息 #{i+1}",
            "device_status": "continuous_test"
        }
        
        print(f"📤 发送第 {i+1}/{count} 条数据...")
        send_json_data(host, port, data)
        time.sleep(2)

def main():
    parser = argparse.ArgumentParser(description='ESP32S3 WiFi数据接收端测试工具')
    parser.add_argument('--host', '-H', default='*************', 
                        help='ESP32S3的IP地址 (默认: *************)')
    parser.add_argument('--port', '-p', type=int, default=8080, 
                        help='ESP32S3的端口 (默认: 8080)')
    parser.add_argument('--test', '-t', choices=['sensor', 'message', 'complex', 'text', 'continuous', 'all'],
                        default='all', help='测试类型 (默认: all)')
    parser.add_argument('--count', '-c', type=int, default=5,
                        help='连续测试的次数 (默认: 5)')
    
    args = parser.parse_args()
    
    print("🚀 ESP32S3 WiFi数据接收端测试工具")
    print("=" * 50)
    print(f"目标地址: {args.host}:{args.port}")
    print(f"测试类型: {args.test}")
    print("=" * 50)
    
    # 测试连接
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect((args.host, args.port))
        sock.close()
        print("✅ 连接测试成功")
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        print("\n请检查:")
        print("1. ESP32S3是否已启动并连接到WiFi")
        print("2. IP地址和端口是否正确")
        print("3. 防火墙设置")
        sys.exit(1)
    
    # 执行测试
    if args.test == 'sensor' or args.test == 'all':
        test_sensor_data(args.host, args.port)
        time.sleep(2)
    
    if args.test == 'message' or args.test == 'all':
        test_message_data(args.host, args.port)
        time.sleep(2)
    
    if args.test == 'complex' or args.test == 'all':
        test_complex_data(args.host, args.port)
        time.sleep(2)
    
    if args.test == 'text' or args.test == 'all':
        test_text_data(args.host, args.port)
        time.sleep(2)
    
    if args.test == 'continuous' or args.test == 'all':
        test_continuous_data(args.host, args.port, args.count)
    
    print("\n🎉 测试完成!")
    print("\n请检查ESP32S3的串口输出和屏幕显示，确认数据接收正常。")

if __name__ == "__main__":
    main()
