# Simple WiFi Receiver Build Script
Write-Host "Building ESP32S3 WiFi Data Receiver..." -ForegroundColor Green

# Get current project directory
$PROJECT_DIR = Get-Location

# Set ESP-IDF path to version 5.4.2
$env:IDF_PATH = "C:\Users\<USER>\esp\v5.4.2\esp-idf"

# Add local Git tools to PATH
$LOCAL_GIT_PATH = "$PROJECT_DIR\tools\idf-git\2.39.2\cmd"
$LOCAL_PYTHON_PATH = "$PROJECT_DIR\tools\idf-python\3.11.2"

# Add Python environment and tools to PATH, including local Git
$env:PATH = "$LOCAL_GIT_PATH;$LOCAL_PYTHON_PATH;D:\esp32-idf-ahy\5.3.2\python_env\idf5.3_py3.11_env\Scripts;D:\esp32-idf-ahy\5.3.2\tools;" + $env:PATH

# Build the project directly
Write-Host "Starting build..." -ForegroundColor Yellow
& "D:\esp32-idf-ahy\5.3.2\python_env\idf5.3_py3.11_env\Scripts\python.exe" "$env:IDF_PATH\tools\idf.py" build

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build completed successfully!" -ForegroundColor Green
    
    if (Test-Path "build\xiaozhi.bin") {
        $binSize = (Get-Item "build\xiaozhi.bin").Length
        Write-Host "Firmware binary created: xiaozhi.bin ($binSize bytes)" -ForegroundColor Cyan
        
        Write-Host "`n========================================" -ForegroundColor Green
        Write-Host "ESP32S3 WiFi Data Receiver Ready!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "Features:" -ForegroundColor Yellow
        Write-Host "- TCP Server on port 8080" -ForegroundColor White
        Write-Host "- JSON data parsing" -ForegroundColor White
        Write-Host "- Multi-client support (max 3)" -ForegroundColor White
        Write-Host "- Real-time display notifications" -ForegroundColor White
        Write-Host "`nNext steps:" -ForegroundColor Yellow
        Write-Host "1. Flash: idf.py flash" -ForegroundColor White
        Write-Host "2. Monitor: idf.py monitor" -ForegroundColor White
        Write-Host "3. Configure WiFi on device" -ForegroundColor White
        Write-Host "4. Note IP address from serial output" -ForegroundColor White
        Write-Host "5. Update PlatformIO sender with IP" -ForegroundColor White
    }
} else {
    Write-Host "Build failed!" -ForegroundColor Red
}
