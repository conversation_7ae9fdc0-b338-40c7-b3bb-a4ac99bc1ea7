# ESP32-S3 蓝牙WiFi桥接器使用指南

## 项目概述

本项目实现了ESP32-S3 PICO-1和ESP32-S3 DevKitC-1之间的蓝牙通信，将WiFi接收的数据通过蓝牙转发给DevKitC-1。

### 系统架构

```
WiFi客户端 → ESP32-S3 PICO-1 (WiFi接收器 + 蓝牙服务器) → ESP32-S3 DevKitC-1 (蓝牙客户端)
```

## 硬件要求

### ESP32-S3 PICO-1 (桥接器端)
- ESP32-S3-PICO-1开发板
- WiFi天线（通常板载）
- 蓝牙天线（通常板载）
- USB-C数据线

### ESP32-S3 DevKitC-1 (接收端)
- ESP32-S3-DevKitC-1开发板
- 蓝牙天线（通常板载）
- USB-C数据线

## 软件要求

- ESP-IDF 5.4.2 或更高版本
- Python 3.8 或更高版本
- Git

## 项目结构

```
project/
├── main/
│   ├── bluetooth/
│   │   ├── bluetooth_server.h/cc      # 蓝牙服务器基础类
│   │   └── wifi_bt_bridge.h/cc        # WiFi到蓝牙桥接器
│   ├── pico_wifi_bt_main.cc           # PICO-1主程序
│   └── components/
│       └── wifi_data_receiver/        # WiFi数据接收组件
├── devkitc_bluetooth_client.h/cc      # DevKitC-1蓝牙客户端
├── devkitc_main.cc                    # DevKitC-1主程序
└── CMakeLists.txt
```

## 配置步骤

### 1. ESP32-S3 PICO-1 配置

#### 1.1 WiFi配置
编辑 `main/pico_wifi_bt_main.cc` 文件，修改WiFi连接信息：

```cpp
#define WIFI_SSID      "YOUR_WIFI_SSID"      // 修改为你的WiFi名称
#define WIFI_PASS      "YOUR_WIFI_PASSWORD"  // 修改为你的WiFi密码
```

#### 1.2 蓝牙设备名称配置
如需修改蓝牙设备名称，在 `pico_wifi_bt_main.cc` 中修改：

```cpp
g_bridge = new WiFiBtBridge("ESP32-S3-PICO-Bridge", 8080, 5);
```

#### 1.3 WiFi服务器端口配置
默认监听端口为8080，如需修改：

```cpp
g_bridge = new WiFiBtBridge("ESP32-S3-PICO-Bridge", 8080, 5);  // 第二个参数是端口
```

### 2. ESP32-S3 DevKitC-1 配置

#### 2.1 目标设备名称配置
编辑 `devkitc_main.cc` 文件，确保目标设备名称与PICO-1一致：

```cpp
g_bt_client = new DevKitCBluetoothClient("ESP32-S3-PICO-Bridge");
```

#### 2.2 自动重连配置
默认启用自动重连，如需禁用：

```cpp
g_bt_client->SetAutoReconnect(false);
```

## 编译和烧录

### ESP32-S3 PICO-1

1. 进入项目目录
2. 设置目标芯片：
   ```bash
   idf.py set-target esp32s3
   ```
3. 配置项目：
   ```bash
   idf.py menuconfig
   ```
   - 在 `Component config → Bluetooth → Bluetooth controller → BR/EDR` 中启用经典蓝牙
   - 在 `Component config → Wi-Fi` 中确保WiFi已启用

4. 编译项目：
   ```bash
   idf.py build
   ```
5. 烧录到PICO-1：
   ```bash
   idf.py -p COM_PORT flash monitor
   ```

### ESP32-S3 DevKitC-1

1. 创建新的ESP-IDF项目
2. 将 `devkitc_bluetooth_client.h/cc` 和 `devkitc_main.cc` 复制到项目中
3. 修改 `CMakeLists.txt` 添加源文件
4. 设置目标芯片：
   ```bash
   idf.py set-target esp32s3
   ```
5. 配置项目（同PICO-1的蓝牙配置）
6. 编译和烧录：
   ```bash
   idf.py build
   idf.py -p COM_PORT flash monitor
   ```

## 使用方法

### 1. 启动顺序

1. **先启动ESP32-S3 PICO-1**
   - 连接到WiFi网络
   - 启动蓝牙服务器
   - 开始监听WiFi数据

2. **再启动ESP32-S3 DevKitC-1**
   - 搜索蓝牙设备
   - 连接到PICO-1
   - 开始接收数据

### 2. 发送测试数据

可以使用以下方法向PICO-1发送WiFi数据：

#### 方法1：使用netcat (Linux/Mac)
```bash
echo "Hello from WiFi client" | nc PICO_IP_ADDRESS 8080
```

#### 方法2：使用Python脚本
```python
import socket

def send_data(ip, port, message):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        sock.connect((ip, port))
        sock.send(message.encode())
        print(f"Sent: {message}")
    finally:
        sock.close()

# 使用示例
send_data("*************", 8080, "Temperature: 25.5°C")
```

#### 方法3：使用curl
```bash
curl -X POST -d "Sensor data: humidity 60%" http://PICO_IP_ADDRESS:8080
```

### 3. 监控日志

#### PICO-1日志示例：
```
I (12345) PICO_MAIN: WiFi connected. IP: *************
I (12346) WiFiBtBridge: WiFi-Bluetooth Bridge started successfully!
I (12347) PICO_MAIN: WiFi Server listening on *************:8080
I (12348) PICO_MAIN: Bluetooth device name: ESP32-S3-PICO-Bridge
I (12349) WiFiBtBridge: Bluetooth client connected
I (12350) WiFiBtBridge: WiFi data received from *************:54321, length: 25
I (12351) WiFiBtBridge: Data forwarded via Bluetooth successfully
```

#### DevKitC-1日志示例：
```
I (12345) DevKitC_Main: Target device found: ESP32-S3-PICO-Bridge
I (12346) DevKitC_Main: Connected to device: ESP32-S3-PICO-Bridge
I (12347) DevKitC_Main: Ready to receive data from WiFi-Bluetooth bridge
I (12348) DevKitC_Main: Data received: 25 bytes
I (12349) DevKitC_Main: Received data: [WiFi:*************:54321] Temperature: 25.5°C
```

## 故障排除

### 常见问题

#### 1. WiFi连接失败
- 检查WiFi SSID和密码是否正确
- 确认WiFi网络支持2.4GHz频段
- 检查WiFi信号强度

#### 2. 蓝牙连接失败
- 确认两个设备的蓝牙设备名称匹配
- 检查蓝牙是否正确初始化
- 尝试重启两个设备

#### 3. 数据传输问题
- 检查WiFi服务器是否正常监听
- 确认蓝牙连接状态
- 查看日志中的错误信息

### 调试技巧

1. **启用详细日志**：
   ```cpp
   esp_log_level_set("*", ESP_LOG_DEBUG);
   ```

2. **检查内存使用**：
   ```cpp
   ESP_LOGI(TAG, "Free heap: %d bytes", esp_get_free_heap_size());
   ```

3. **监控任务状态**：
   ```cpp
   vTaskList(task_list_buffer);
   ESP_LOGI(TAG, "Task list:\n%s", task_list_buffer);
   ```

## 性能优化

### 1. 内存优化
- 调整任务堆栈大小
- 优化缓冲区大小
- 及时释放不用的内存

### 2. 传输优化
- 调整WiFi接收缓冲区大小
- 优化蓝牙传输包大小
- 使用队列缓存数据

### 3. 功耗优化
- 使用轻度睡眠模式
- 动态调整CPU频率
- 优化WiFi省电模式

## 扩展功能

### 1. 数据加密
可以在桥接器中添加数据加密功能：

```cpp
// 在转发数据前加密
std::string encrypted_data = encrypt(data, encryption_key);
bt_server_->SendString(encrypted_data);
```

### 2. 数据压缩
对于大量数据，可以添加压缩功能：

```cpp
// 压缩数据
std::vector<uint8_t> compressed = compress_data(data, length);
bt_server_->SendData(compressed.data(), compressed.size());
```

### 3. 多客户端支持
可以修改代码支持多个DevKitC-1同时连接。

## 技术支持

如果遇到问题，请检查：
1. ESP-IDF版本兼容性
2. 硬件连接是否正确
3. 配置参数是否正确
4. 日志输出中的错误信息

更多技术细节请参考ESP-IDF官方文档和本项目的源代码注释。
