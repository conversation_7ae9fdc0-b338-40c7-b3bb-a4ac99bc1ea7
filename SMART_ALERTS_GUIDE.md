# 小智智能提醒功能使用指南

## 🎯 功能概述

小智现在具备智能提醒功能，能够：
- 📊 **统计分析**: 记录最近5次接收到的表情和坐姿数据
- 🧠 **智能判断**: 当某种状态在5次数据中出现3次以上时触发提醒
- 🔊 **语音反馈**: 通过TTS语音播报和显示屏提供个性化提醒
- 🎵 **音效提示**: 播放振动音效引起注意
- ⏰ **冷却机制**: 5秒冷却时间，避免重复提醒（测试版）

## 📋 工作原理

### 数据统计
- 维护最近5次的表情历史记录
- 维护最近5次的坐姿历史记录
- 自动滚动更新，保持最新数据

### 触发条件
- **表情提醒**: Sad、Angry、Happy在5次中出现≥3次
- **坐姿提醒**: Poor Posture、Mild Poor Posture在5次中出现≥3次

### 冷却机制
- 每次提醒后5秒内不会重复相同类型的提醒（测试版，正式版为30秒）
- 防止频繁打扰用户

### 语音播报流程
1. **音效提示**: 首先播放振动音效引起注意
2. **TTS请求**: 500ms后发送TTS请求给服务器
3. **语音播报**: 服务器返回语音数据，小智播报提醒内容

## 🎭 表情提醒

### 支持的表情状态
| 表情状态 | 触发条件 | 提醒消息 |
|---------|---------|---------|
| Sad | 5次中≥3次 | "我注意到你最近看起来有些难过，要不要聊聊天？" |
| Angry | 5次中≥3次 | "你看起来有点生气，深呼吸一下，放松心情吧。" |
| Happy | 5次中≥3次 | "你看起来心情很好呢，真棒！" |

### 忽略的状态
- `No Face`: 未检测到人脸时不计入统计

## 🪑 坐姿提醒

### 支持的坐姿状态
| 坐姿状态 | 触发条件 | 提醒消息 |
|---------|---------|---------|
| Poor Posture | 5次中≥3次 | "我发现你的坐姿不太好，记得挺直腰背哦！" |
| Mild Poor Posture | 5次中≥3次 | "我发现你的坐姿不太好，记得挺直腰背哦！" |
| Moderate Poor Posture | 5次中≥3次 | "我发现你的坐姿不太好，记得挺直腰背哦！" |
| Severe Poor Posture | 5次中≥3次 | "我发现你的坐姿不太好，记得挺直腰背哦！" |
| Good Posture | 5次中≥3次 | "你的坐姿很端正，继续保持！" |

### 忽略的状态
- `No People`: 未检测到人时不计入统计

## 📊 数据格式

### 输入数据格式
```json
{
    "emotion": "Sad",
    "posture": "Poor Posture", 
    "confidence": 0.85,
    "timestamp": 1642678800
}
```

### 日志输出示例
```
I (24933) Application: 🎭 Emotion: Sad
I (24943) Application: 🪑 Posture: Poor Posture
I (24953) Application: 📊 Emotion history size: 3
I (24963) Application: 📊 Posture history size: 3
I (24973) Application: 📊 State 'Sad' appears 3 times in last 5 records
I (24983) Application: 🚨 Smart Alert: 我注意到你最近看起来有些难过，要不要聊聊天？
```

## 🧪 测试方法

### 1. 使用测试脚本
```bash
python test_smart_alerts.py
```

### 2. 手动测试
```python
import socket
import json
import time

def send_test_data(emotion, posture):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect(("192.168.146.22", 8080))
    
    data = {
        "emotion": emotion,
        "posture": posture,
        "confidence": 0.85,
        "timestamp": int(time.time() * 1000)
    }
    
    sock.send(json.dumps(data).encode('utf-8'))
    sock.close()

# 测试连续5次Sad表情
for i in range(5):
    send_test_data("Sad", "Good Posture")
    time.sleep(2)
```

## 📈 测试场景

### 场景1: 表情提醒测试
1. 连续发送5次"Sad"表情
2. 预期：第5次时触发语音提醒

### 场景2: 坐姿提醒测试
1. 连续发送5次"Poor Posture"
2. 预期：第5次时触发语音提醒

### 场景3: 混合数据测试
1. 发送：Happy, Angry, Calm, Angry, Angry
2. 预期：Angry出现3次，触发提醒

### 场景4: 冷却机制测试
1. 先触发一次提醒
2. 立即再次尝试触发相同提醒
3. 预期：第二次被冷却机制阻止

## 🔧 配置参数

### 可调整的参数
```cpp
// 在 application.h 中的 DataHistory 结构体
static const size_t MAX_HISTORY_SIZE = 5;      // 历史记录大小
static const size_t ALERT_THRESHOLD = 3;       // 触发阈值
static const uint32_t ALERT_COOLDOWN_MS = 30000; // 冷却时间(毫秒)
```

### 修改建议
- **增加历史记录**: 提高 `MAX_HISTORY_SIZE` 到7或10
- **降低敏感度**: 提高 `ALERT_THRESHOLD` 到4或5
- **调整冷却时间**: 根据使用场景调整 `ALERT_COOLDOWN_MS`

## 🚨 注意事项

### 1. 网络连接
- 确保小智设备已连接WiFi
- 确认IP地址和端口正确

### 2. 数据质量
- 置信度建议≥0.8
- 避免发送"No Face"或"No People"数据

### 3. 使用建议
- 数据发送间隔建议2-5秒
- 避免过于频繁的数据发送

## 🤖 **MCP工具支持**

小智现在支持通过MCP工具查询表情和坐姿状态：

### 可用的MCP工具
1. **`self.get_emotion_status`** - 获取当前表情状态和历史
2. **`self.get_posture_status`** - 获取当前坐姿状态和历史

### 使用示例
用户可以问小智：
- "我现在的表情怎么样？"
- "我的坐姿状态如何？"
- "最近我的情绪变化如何？"
- "我需要调整坐姿吗？"

小智会调用相应的MCP工具获取详细信息并回答。

## 🔍 故障排除

### 问题1: 没有语音提醒
- 检查数据是否达到触发条件（5次中≥3次）
- 确认不在冷却期内
- 检查小智音量设置
- 确认使用了正确的坐姿类型名称

### 问题2: 重复提醒
- 检查冷却机制是否正常工作
- 确认时间戳正确

### 问题3: 连接失败
- 检查网络连接
- 确认IP地址和端口
- 检查防火墙设置

### 问题4: 小智无法回答状态问题
- 确认MCP工具已正确注册
- 检查是否有数据历史记录
- 尝试重启小智设备

## 📞 技术支持

如遇到问题，请提供：
1. 完整的串口日志
2. 发送的数据格式和内容
3. 预期行为和实际行为描述
4. 网络配置信息

---

🎉 **恭喜！小智现在具备了智能提醒功能，能够更好地关心您的健康状态！**
