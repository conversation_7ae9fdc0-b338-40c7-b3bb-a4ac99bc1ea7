# 表情与坐姿状态显示功能使用指南

## 🎯 功能概述

本功能已成功集成到ESP32S3接收端，可以接收并显示来自PlatformIO发送端的表情和坐姿状态数据。

### ✨ 主要特性
- 🎭 **表情显示**: 接收并显示5种表情状态（Happy, Sad, Angry, Excited, Calm）
- 🪑 **坐姿状态**: 显示坐姿质量（Good Posture, Poor Posture）
- 📊 **置信度显示**: 显示检测置信度
- 🔄 **实时更新**: 实时更新小智的表情和状态消息
- 📱 **通知提醒**: 在屏幕上显示状态变化通知

## 🚀 快速开始

### 1. 编译和烧录接收端

```bash
# 编译项目
powershell -ExecutionPolicy Bypass -File build_simple.ps1

# 烧录到设备
idf.py flash monitor
```

### 2. 确认WiFi连接

确保ESP32S3设备已连接到WiFi网络，并记录其IP地址（通常显示在串口输出中）。

### 3. 测试功能

使用提供的测试脚本：

```bash
python test_emotion_posture_display.py
```

## 📊 数据格式

### JSON数据格式
```json
{
    "emotion": "Happy",
    "posture": "Good Posture", 
    "confidence": 0.85,
    "timestamp": 1642678800
}
```

### 字段说明
- `emotion`: 表情状态（Happy, Sad, Angry, Excited, Calm）
- `posture`: 坐姿状态（Good Posture, Poor Posture）
- `confidence`: 检测置信度（0.0-1.0）
- `timestamp`: 时间戳（可选）

## 🎭 表情映射

| 发送端表情 | 小智表情 | 显示效果 |
|-----------|---------|---------|
| Happy     | happy   | 开心表情 |
| Sad       | sad     | 伤心表情 |
| Angry     | angry   | 生气表情 |
| Excited   | excited | 兴奋表情 |
| Calm      | calm    | 平静表情 |

## 🪑 坐姿状态处理

### Good Posture（良好坐姿）
- 显示通知："🪑 坐姿: Good Posture"
- 系统消息："坐姿端正，保持良好！"

### Poor Posture（不良坐姿）
- 显示通知："🪑 坐姿: Poor Posture"
- 系统消息："坐姿不端正，请调整一下"

## 🔧 技术实现

### 数据接收流程
1. WiFi数据接收器监听TCP端口8080
2. 接收JSON格式的数据包
3. 解析表情和坐姿字段
4. 更新小智的表情显示
5. 显示状态通知和系统消息

### 核心代码位置
- **数据接收**: `main/application.cc` - `OnWifiDataReceived()`
- **WiFi组件**: `components/wifi_data_receiver/`
- **显示控制**: 通过`Board::GetInstance().GetDisplay()`

## 📱 用户界面

### 表情显示
- 小智的表情会根据接收到的emotion字段实时变化
- 支持的表情包括开心、伤心、生气、兴奋、平静

### 通知系统
- 表情变化通知：显示3秒
- 坐姿状态通知：显示3秒
- 系统消息：持续显示在聊天区域

### 日志输出
```
=== WiFi Data Received ===
From: 192.168.146.75:58015
Length: 83 bytes
Data: {"emotion":"Happy","posture":"Good Posture","confidence":0.85,"timestamp":357431}
🎭 Emotion: Happy
🪑 Posture: Good Posture
📊 Confidence: 0.85
```

## 🧪 测试方法

### 1. 使用测试脚本
```bash
python test_emotion_posture_display.py
```

### 2. 手动发送数据
```python
import socket
import json

sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
sock.connect(("**************", 8080))

data = {
    "emotion": "Happy",
    "posture": "Good Posture",
    "confidence": 0.95
}

sock.send(json.dumps(data).encode('utf-8'))
sock.close()
```

### 3. 验证功能
- ✅ 小智表情是否正确变化
- ✅ 屏幕通知是否正常显示
- ✅ 系统消息是否更新
- ✅ 串口日志是否正确输出

## 🔍 故障排除

### 1. 连接问题
- 确认ESP32S3和发送端在同一WiFi网络
- 检查IP地址是否正确
- 验证端口8080是否开放

### 2. 数据不显示
- 检查JSON格式是否正确
- 确认字段名称拼写正确
- 查看串口日志了解详细信息

### 3. 表情不变化
- 确认emotion字段值在支持列表中
- 检查小智表情映射是否正确
- 验证显示器是否正常工作

## 📈 扩展功能

### 可能的改进
1. **更多表情**: 添加更多表情状态支持
2. **坐姿细分**: 支持更详细的坐姿分类
3. **历史记录**: 记录表情和坐姿变化历史
4. **语音反馈**: 根据状态变化提供语音提醒
5. **数据统计**: 提供表情和坐姿的统计分析

### 集成建议
- 可与健康监控系统集成
- 支持多设备数据聚合
- 添加用户个性化设置
- 实现数据云端同步

## ✅ 功能状态

- ✅ WiFi数据接收功能正常
- ✅ JSON数据解析正常
- ✅ 表情显示功能正常
- ✅ 坐姿状态显示正常
- ✅ 通知系统正常
- ✅ 编译和烧录正常

功能已完全就绪，可以立即使用！🎉
